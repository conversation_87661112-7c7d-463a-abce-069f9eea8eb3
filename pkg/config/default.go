package config

var defaultConfig = []byte(`
env: local
log_level: info
port: 3014
http_port: 8000
skyhub_flights_service_endpoint: localhost:3014

write_url: *******************************************
read_url: *******************************************
rabbitmq_url: amqp://myuser:mypassword@localhost:5672
rabbitmq_suffix:
rabbitmq_queue_type: classic
auth_signing_key: xxxxxx
internal_secret_token: xxxxxx
mongo_db: peahoki-user
public_paths:
- /

public_methods:

internal_methods:
- /skyhub.backend.BookingService/GetBookings
- /skyhub.backend.BookingService/GetBookingByCode
- /skyhub.backend.BookingService/UpdateTicketAndReservationCodes
- /skyhub.backend.BookingService/CancelBooking

redis_address: localhost:6379,
redis_password: xxx,
redis_database: 0
redis_common_database: 0
redis_sentinel: false
redis_sentinel_master_name: mymaster

partner_service_endpoint: partner-service:3000
airplane_service_endpoint: airplane-service:3000
partnership_service_endpoint: partnership-service:3000

amadeus_base_url: https://localhost:3016/amadeus

travel_fusion_supplier_airlines: 
amadeus_exclusion_airlines:
decrypt_key: xxx

travel_fusion_url: https://api.travelfusion.com/api-v2
travel_fusion_username: xxx
travel_fusion_password: xxx

vna_url: https://sws-crt.cert.havail.sabre.com/vn/websvc
vna_username: xxx
vna_password: xxx
vna_organization: xxx
vna_domain: VN
vna_pcc: VN
vna_printer_country: GF
vna_printer_lniata: AB31D2
vna_station_number: ********
vna_account_number: ****************
vietjet_url: https://uat-vietjetapi.vietjetair.com/api/
vietjet_username: xxx
vietjet_password: xxx
vietjet_base_path: /
vietjet_api_key: xxx
service_ip: **************

at_vietjet_url: https://uat-vietjetapi.vietjetair.com/api/
at_vietjet_username: xxx
at_vietjet_password: xxx
at_vietjet_base_path: /
at_vietjet_api_key: xxx
at_service_ip: **************

hub_partnership_id: 652618f6f511734dec28d649
order_service_endpoint: localhost:3007
wallet_service_endpoint: localhost:3012
payment_service_endpoint: localhost:3002
data_warehouse_service_endpoint: http://localhost:9009
notification_service_endpoint: localhost:3010
price_service_endpoint: localhost:3019

job_scan_pending_ticket_schedule: xxx
job_save_tf_suppliers_route_daily: 0 1 * * *
job_scan_expired_booking_schedule: xx
job_collect_l2b_tracking: xx
travel_fusion_time_zone: Europe/London
enabled_providers: amadeus,travelfusion,vietnam-airlines,vietjet-airlines,ev,ev-international

wallet_exchange: wallet_exchange
wallet_deposit_processing_route: wallet.top_up
ev_international_base_url: https://demo.booking1a.com/services/proxy/api/flights
ev_international_secure_base_url: https://secure-demo.booking1a.com/realms/jhipster/protocol/openid-connect/token
ev_international_client_secret:  nMAPiLJnd9HveLnUg38F85OgG6OOS0va
ev_international_password: xxxxxx
ev_international_client_id: web_app
ev_international_username: OTAAPIUAT
ev_international_grant_type: password

telegram_url: https://api.telegram.org
telegram_ev_international_bot_token: **********************************************
telegram_ev_international_chat_id : -**********
telegram_issue_emd_bot_token: xxxxxxxxxxxxx
telegram_issue_emd_chat_id : xxxxxxxxxx
telegram_ssr_notify_bot_token : **********************************************
telegram_notify_chat_id: -*************
telegram_pkfare_issuing_chat_id: xxxx

ev_url: https://api.bsp.onlineairticket.vn
ev_username: xx
ev_password: xxx
request_log_type: file
ev_enable_airline: 
ev_mail_receiver: <EMAIL>
data_warehouse_api_key: xxxx

vna1a_private_key: xxxx
vna1a_account: xxxx
vna1a_password: xxxx
vna1a_token: xxxx
vna1a_ip_address: xxxx
vna1a_base_url: xxxx
payment_portal_auto_approve: false
user_id_auto_approve: xxxx

hnh_base_url: xxx
hnh_header_user: xxx
hnh_header_pass: xxx
hnh_agent_account: xxx
hnh_agent_password: xxx
hnh_product_key: xxx
issue_duration_default: 30
search_flight_ctx_timeout: 90
manual_issuing_airlines: ""
manual_issuing_active_time: ""

filtered_airlines_offices: xxx
disable_report_salesforce_egb: false
hnh_enable_vj: false

bizitrip_email: <EMAIL>
vmb_email: <EMAIL>
hpl_email: <EMAIL>

pkfare_base_url: https://api.pkfare.com
pkfare_partner_id: xxxx
pkfare_partner_key: xxxx

tongcheng_base_url: http://www.uat.itravelsaas.com
tongcheng_username: xxxx
tongcheng_partner_id: xxxx
tongcheng_partner_key: xxxx

internal_booking_offices: ""

company_email: xxx
company_name: xxx
company_phone: xxx

at_company_email: xxx
at_company_name: xxx
at_company_phone: xxx

hnh_agent_email: xxx
hnh_agent_name: xxx
hnh_agent_phone: xxx

hnh_agent_email_bizi_trip: xxx
hnh_agent_name_bizi_trip: xxx
hnh_agent_phone_bizi_trip: xxx

hnh_hpl_base_url: xxx
hnh_hpl_header_user: xxx
hnh_hpl_header_pass: xxx
hnh_hpl_agent_account: xxx
hnh_hpl_agent_password: xxx
hnh_hpl_product_key: xxx

hnh_hpl_agent_email: xxx
hnh_hpl_agent_name: xxx
hnh_hpl_agent_phone: xxx
`)
