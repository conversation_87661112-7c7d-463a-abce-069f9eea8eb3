package config

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/joho/godotenv"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"
	"gitlab.deepgate.io/apps/common/constants"
)

type Schema struct {
	Env                     string   `json:"env"`
	LogLevel                string   `json:"log_level"`
	Port                    string   `json:"port"`
	HTTPPort                string   `json:"http_port"`
	WriteURL                string   `json:"write_url"`
	ReadURL                 string   `json:"read_url"`
	MongoDB                 string   `json:"mongo_db"`
	RabbitmqURL             string   `json:"rabbitmq_url"`
	RabbitmqSuffix          string   `json:"rabbitmq_suffix"`
	RabbitmqQueueType       string   `json:"rabbitmq_queue_type"`
	RedisAddress            string   `json:"redis_address"`
	RedisPassword           string   `json:"redis_password"`
	RedisDatabase           int      `json:"redis_database"`
	RedisCommonDatabase     int      `json:"redis_common_database"`
	RedisSentinel           bool     `json:"redis_sentinel"`
	RedisSentinelMasterName string   `json:"redis_sentinel_master_name"`
	AuthSigningKey          string   `json:"auth_signing_key"`
	AuthTokenTTL            uint     `json:"auth_token_ttl"`
	PublicPaths             []string `json:"public_paths"`
	PublicMethods           []string `json:"public_methods"`
	InternalMethods         []string `json:"internal_methods"`
	InternalSecretToken     string   `json:"internal_secret_token"`
	DecryptKey              string   `json:"decrypt_key"`

	TravelFusionURL      string `json:"travel_fusion_url"`
	TravelFusionUsername string `json:"travel_fusion_username"`
	TravelFusionPassword string `json:"travel_fusion_password"`

	VNABaseURL        string `json:"vna_url"`
	VNAUsername       string `json:"vna_username"`
	VNAPassword       string `json:"vna_password"`
	VNAOrganization   string `json:"vna_organization"`
	VNADomain         string `json:"vna_domain"`
	VNAPrinterCountry string `json:"vna_printer_country"`
	VNAPrinterLNIATA  string `json:"vna_printer_lniata"`
	VNAPCC            string `json:"vna_pcc"`
	VNAStationNbr     string `json:"vna_station_number"`
	VNAAccountNbr     string `json:"vna_account_number"`
	SoapEnvNameSpace  string `json:"soap_env_name_space"`
	EBNameSpace       string `json:"eb_name_space"`
	XLinkNameSpace    string `json:"xlink_name_space"`
	WSSENameSpace     string `json:"wsse_name_space"`
	NSNameSpace       string `json:"ns_name_space"`

	FromPartyID string `json:"from_party_id"`
	ToPartyID   string `json:"to_party_id"`
	CPAID       string `json:"cpa_id"`

	AmadeusBaseURL               string   `json:"amadeus_base_url"`
	TravelFusionSupplierAirlines []string `json:"travel_fusion_supplier_airlines"`
	AmadeusExclusionAirlines     []string `json:"amadeus_exclusion_airlines"`
	PartnerServiceEndpoint       string   `json:"partner_service_endpoint"`
	AirplaneServiceEndpoint      string   `json:"airplane_service_endpoint"`
	PartnershipServiceEndpoint   string   `json:"partnership_service_endpoint"`
	DataWareHouseServiceEndpoint string   `json:"data_warehouse_service_endpoint"`
	NotificationServiceEndpoint  string   `json:"notification_service_endpoint"`
	PriceServiceEndpoint         string   `json:"price_service_endpoint"`
	HubPartnershipID             string   `json:"hub_partnership_id"`
	OrderServiceEndpoint         string   `json:"order_service_endpoint"`
	WalletServiceEndpoint        string   `json:"wallet_service_endpoint"`
	PaymentServiceEndpoint       string   `json:"payment_service_endpoint"`
	SKyhubFlightsServiceEndpoint string   `json:"skyhub_flights_service_endpoint"`

	VietjetURL      string `json:"vietjet_url"`
	VietjetUsername string `json:"vietjet_username"`
	VietjetPassword string `json:"vietjet_password"`
	VietjetBasePath string `json:"vietjet_base_path"`
	VietjetAPIKey   string `json:"vietjet_api_key"`
	ServiceIP       string `json:"service_ip"`

	ATVietjetURL      string `json:"at_vietjet_url"`
	ATVietjetUsername string `json:"at_vietjet_username"`
	ATVietjetPassword string `json:"at_vietjet_password"`
	ATVietjetBasePath string `json:"at_vietjet_base_path"`
	ATVietjetAPIKey   string `json:"at_vietjet_api_key"`
	ATServiceIP       string `json:"at_service_ip"`

	ATCompanyEmail string `json:"at_company_email"`
	ATCompanyName  string `json:"at_company_name"`
	ATCompanyPhone string `json:"at_company_phone"`

	JobScanSpendingTicketSchedule string `json:"job_scan_pending_ticket_schedule"`
	JobSaveTFSuppliersRouteDaily  string `json:"job_save_tf_suppliers_route_daily"`
	JobScanExpiredBookingSchedule string `json:"job_scan_expired_booking_schedule"`
	JobCollectL2bTracking         string `json:"job_collect_l2b_tracking"`
	TravelFusionTimeZone          string `json:"travel_fusion_time_zone"`

	EnabledProviders string `json:"enabled_providers"`

	WalletExchange               string `json:"wallet_exchange"`
	WalletDepositProcessingRoute string `json:"wallet_deposit_processing_route"`

	EVInternationalBaseUrl        string `json:"ev_international_base_url"`
	EVInternationalBasePath       string `json:"ev_international_base_path"`
	EVInternationalSecureBaseUrl  string `json:"ev_international_secure_base_url"`
	EVInternationalSecureBasePath string `json:"ev_international_secure_base_path"`
	EVInternationalClientSecret   string `json:"ev_international_client_secret"`
	EVInternationalPassword       string `json:"ev_international_password"`
	EVInternationalClientID       string `json:"ev_international_client_id"`
	EVInternationalUsername       string `json:"ev_international_username"`
	EVInternationalGrantType      string `json:"ev_international_grant_type"`

	TelegramURL                     string `json:"telegram_url"`
	TelegramEVInternationalBotToken string `json:"telegram_ev_international_bot_token"`
	TelegramEVInternationalChatID   string `json:"telegram_ev_international_chat_id"`
	TelegramIssueEMDBotToken        string `json:"telegram_issue_emd_bot_token"`
	TelegramIssueEMDChatID          string `json:"telegram_issue_emd_chat_id"`
	TelegramSSRNotifyBotToken       string `json:"telegram_ssr_notify_bot_token"`
	TelegramSSrNotifyChatID         string `json:"telegram_ssr_notify_chat_id"`
	TelegramNotifyChatID            string `json:"telegram_notify_chat_id"`
	TelegramPkfareIssuingChatID     string `json:"telegram_pkfare_issuing_chat_id"`

	EvURL                  string `json:"ev_url"`
	EvUsername             string `json:"ev_username"`
	EvPassword             string `json:"ev_password"`
	EvEnableAirline        string `json:"ev_enable_airline"`
	EvMailReceiver         string `json:"ev_mail_receiver"`
	ReqLogType             string `json:"request_log_type"`
	DataWareHouseAPIKey    string `json:"data_warehouse_api_key"`
	SearchFlightCtxTimeout int    `json:"search_flight_ctx_timeout"`

	VNA1APrivateKey          string `json:"vna1a_private_key"`
	VNA1AAccount             string `json:"vna1a_account"`
	VNA1APassword            string `json:"vna1a_password"`
	VNA1AToken               string `json:"vna1a_token"`
	VNA1AIPAddress           string `json:"vna1a_ip_address"`
	VNA1ABaseURL             string `json:"vna1a_base_url"`
	PaymentPortalAutoApprove bool   `json:"payment_portal_auto_approve"`
	UserIDAutoApprove        string `json:"user_id_auto_approve"`

	HNHBaseURL       string `json:"hnh_base_url"`
	HNHHeaderUser    string `json:"hnh_header_user"`
	HNHHeaderPass    string `json:"hnh_header_pass"`
	HNHAgentAccount  string `json:"hnh_agent_account"`
	HNHAgentPassword string `json:"hnh_agent_password"`
	HNHProductKey    string `json:"hnh_product_key"`

	HNHHPLBaseURL       string `json:"hnh_hpl_base_url"`
	HNHHPLHeaderUser    string `json:"hnh_hpl_header_user"`
	HNHHPLHeaderPass    string `json:"hnh_hpl_header_pass"`
	HNHHPLAgentAccount  string `json:"hnh_hpl_agent_account"`
	HNHHPLAgentPassword string `json:"hnh_hpl_agent_password"`
	HNHHPLProductKey    string `json:"hnh_hpl_product_key"`

	IssueDurationDefault int `json:"issue_duration_default"`

	ManualIssuingAirlines   string `json:"manual_issuing_airlines"`
	ManualIssuingActiveTime string `json:"manual_issuing_active_time"`

	FilteredAirlinesOffices string `json:"filtered_airlines_offices"`

	DisableReportSalesforceEGB bool   `json:"disable_report_salesforce_egb"`
	BizitripEmail              string `json:"bizitrip_email"`
	VMBEmail                   string `json:"vmb_email"`

	PkfareBaseURL    string `json:"pkfare_base_url"`
	PkfarePartnerID  string `json:"pkfare_partner_id"`
	PkfarePartnerKey string `json:"pkfare_partner_key"`

	InternalBookingOffices string `json:"internal_booking_offices"`
	CompanyEmail           string `json:"company_email"`
	CompanyName            string `json:"company_name"`
	CompanyPhone           string `json:"company_phone"`

	HNHAgentEmail string `json:"hnh_agent_email"`
	HNHAgentName  string `json:"hnh_agent_name"`
	HNHAgentPhone string `json:"hnh_agent_phone"`

	HPLEmail         string `json:"hpl_email"`
	HNHHPLAgentEmail string `json:"hnh_hpl_agent_email"`
	HNHHPLAgentName  string `json:"hnh_hpl__agent_name"`
	HNHHPLAgentPhone string `json:"hnh_hpl_agent_phone"`

	HNHAgentEmailBiziTrip string `json:"hnh_agent_email_bizi_trip"`
	HNHAgentNameBiziTrip  string `json:"hnh_agent_name_bizi_trip"`
	HNHAgentPhoneBiziTrip string `json:"hnh_agent_phone_bizi_trip"`

	HNHEnableVJ bool `json:"hnh_enable_vj"`

	TongChengBaseURL    string `json:"tongcheng_base_url"`
	TongChengUsername   string `json:"tongcheng_username"`
	TongChengPartnerID  string `json:"tongcheng_partner_id"`
	TongChengPartnerKey string `json:"tongcheng_partner_key"`
	ProxyURL            string `json:"proxy_url"`
}

func New() (*Schema, error) {
	err := godotenv.Load()
	if err != nil {
		panic(err)
	}

	v := viper.New()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "__"))
	v.AutomaticEnv()
	v.SetConfigType("yaml")

	if err := v.ReadConfig(bytes.NewBuffer(defaultConfig)); err != nil {
		return nil, err
	}

	cfg := Schema{}
	err = v.Unmarshal(&cfg, func(c *mapstructure.DecoderConfig) {
		c.TagName = "json"
	})

	if cfg.Env != constants.ProductionEnvName {
		c, _ := json.MarshalIndent(cfg, "", "\t")
		fmt.Println("Config:", string(c))
	}

	if cfg.AuthTokenTTL == 0 {
		const defaultTokenTTL = 86400
		cfg.AuthTokenTTL = defaultTokenTTL
	}

	return &cfg, err
}
