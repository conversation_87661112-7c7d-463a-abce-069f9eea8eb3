package converts

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
)

func TestFromTongChengBookingResponse_Success(t *testing.T) {
	// Arrange
	tcResponse := &entities.BookingResponse{
		Response: entities.Response{
			IsSuccess: true,
		},
		OrderNo:   "TC123456789",
		PnrCode:   "ABC123",
		GmtPayOut: "2024-12-11 11:55:00",
	}

	routing := &entities.Routing{
		AdultPrice: 1000.0,
		ChildPrice: 500.0,
		AdultTax:   100.0,
		ChildTax:   50.0,
		Currency:   "CNY",
	}

	// Act
	result, err := FromTongChengBookingResponse(tcResponse, routing)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "TC123456789", result.BookingRef)
	assert.Equal(t, "ABC123", result.OrderNumRef)
	assert.Equal(t, "TC", result.AirlineSystem)
	assert.False(t, result.SkipDefaultLastTktDate)
	assert.Equal(t, int64(0), result.FareExpiredDate)
	assert.Equal(t, int64(0), result.TicketExpiredDate)
	assert.Nil(t, result.CommRate)
	assert.Nil(t, result.SeatError)

	// Verify ExpectedPrice
	assert.NotNil(t, result.ExpectedPrice)
	assert.Equal(t, 1650.0, result.ExpectedPrice.Amount) // 1000+500+100+50
	assert.Equal(t, "CNY", result.ExpectedPrice.Currency)

	// Verify LastTicketingDate is parsed correctly
	assert.Greater(t, result.LastTicketingDate, int64(0))
}

func TestFromTongChengBookingResponse_NilResponse(t *testing.T) {
	// Act
	result, err := FromTongChengBookingResponse(nil, nil)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "TongCheng booking response is nil")
}

func TestFromTongChengBookingResponse_FailedBooking(t *testing.T) {
	// Arrange
	tcResponse := &entities.BookingResponse{
		Response: entities.Response{
			IsSuccess: false,
			ErrorCode: "2001",
			ErrorMsg:  "Flight not available",
		},
	}

	// Act
	result, err := FromTongChengBookingResponse(tcResponse, nil)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "TongCheng booking failed")
	assert.Contains(t, err.Error(), "2001")
	assert.Contains(t, err.Error(), "Flight not available")
}

func TestFromTongChengBookingResponseAdvanced_WithOptions(t *testing.T) {
	// Arrange
	tcResponse := &entities.BookingResponse{
		Response: entities.Response{
			IsSuccess: true,
		},
		OrderNo:   "TC987654321",
		PnrCode:   "XYZ789",
		GmtPayOut: "", // Empty payout date to test fallback
	}

	routing := &entities.Routing{
		AdultPrice: 2000.0,
		AdultTax:   200.0,
		Currency:   "USD",
	}

	commissionRate := 0.05
	options := &BookingConvertOptions{
		CommissionRate:         &commissionRate,
		DefaultCurrency:        "EUR",
		SkipDefaultLastTktDate: true,
		FallbackLastTktHours:   48,
		AirlineSystemOverride:  "TC_PREMIUM",
	}

	// Act
	result, err := FromTongChengBookingResponseAdvanced(tcResponse, routing, options)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "TC987654321", result.BookingRef)
	assert.Equal(t, "XYZ789", result.OrderNumRef)
	assert.Equal(t, "TC_PREMIUM", result.AirlineSystem)
	assert.True(t, result.SkipDefaultLastTktDate)
	assert.NotNil(t, result.CommRate)
	assert.Equal(t, 0.05, *result.CommRate)

	// Verify ExpectedPrice uses routing currency, not default
	assert.NotNil(t, result.ExpectedPrice)
	assert.Equal(t, 2200.0, result.ExpectedPrice.Amount) // 2000+200
	assert.Equal(t, "USD", result.ExpectedPrice.Currency) // From routing, not default EUR

	// Verify fallback LastTicketingDate (should be ~48 hours from now)
	expectedTime := time.Now().Add(48 * time.Hour).UnixMilli()
	assert.InDelta(t, expectedTime, result.LastTicketingDate, float64(5*time.Minute.Milliseconds()))
}

func TestParseTongChengPayoutDate_ValidFormats(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected bool
	}{
		{"Standard format", "2024-12-11 11:55:00", true},
		{"ISO format", "2024-12-11T11:55:00", true},
		{"ISO with timezone", "2024-12-11T11:55:00Z", true},
		{"Alternative format", "11/12/2024 11:55:00", true},
		{"Empty string", "", false},
		{"Invalid format", "invalid-date", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := parseTongChengPayoutDate(tc.input)
			
			if tc.expected {
				assert.NoError(t, err)
				assert.Greater(t, result, int64(0))
			} else {
				assert.Error(t, err)
				assert.Equal(t, int64(0), result)
			}
		})
	}
}

func TestCalculateExpectedPriceFromRouting_EdgeCases(t *testing.T) {
	testCases := []struct {
		name     string
		routing  *entities.Routing
		expected *float64 // nil means should return nil
	}{
		{
			name:     "Nil routing",
			routing:  nil,
			expected: nil,
		},
		{
			name: "Zero prices",
			routing: &entities.Routing{
				AdultPrice: 0,
				ChildPrice: 0,
				AdultTax:   0,
				ChildTax:   0,
				Currency:   "CNY",
			},
			expected: nil,
		},
		{
			name: "Valid prices",
			routing: &entities.Routing{
				AdultPrice: 1500.0,
				ChildPrice: 750.0,
				AdultTax:   150.0,
				ChildTax:   75.0,
				Currency:   "USD",
			},
			expected: func() *float64 { v := 2475.0; return &v }(),
		},
		{
			name: "Empty currency defaults to CNY",
			routing: &entities.Routing{
				AdultPrice: 1000.0,
				Currency:   "",
			},
			expected: func() *float64 { v := 1000.0; return &v }(),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := calculateExpectedPriceFromRouting(tc.routing)
			
			if tc.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, *tc.expected, result.Amount)
				
				expectedCurrency := "CNY"
				if tc.routing != nil && tc.routing.Currency != "" {
					expectedCurrency = tc.routing.Currency
				}
				assert.Equal(t, expectedCurrency, result.Currency)
			}
		})
	}
}

func TestHandleTongChengBookingError(t *testing.T) {
	testCases := []struct {
		name         string
		response     *entities.BookingResponse
		expectError  bool
		errorContains string
	}{
		{
			name:         "Nil response",
			response:     nil,
			expectError:  true,
			errorContains: "response is nil",
		},
		{
			name: "Success response",
			response: &entities.BookingResponse{
				Response: entities.Response{IsSuccess: true},
			},
			expectError: false,
		},
		{
			name: "Known error code",
			response: &entities.BookingResponse{
				Response: entities.Response{
					IsSuccess: false,
					ErrorCode: "2001",
					ErrorMsg:  "Flight not available",
				},
			},
			expectError:   true,
			errorContains: "Flight not available",
		},
		{
			name: "Unknown error code",
			response: &entities.BookingResponse{
				Response: entities.Response{
					IsSuccess: false,
					ErrorCode: "9999",
					ErrorMsg:  "Unknown error",
				},
			},
			expectError:   true,
			errorContains: "Unknown error",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := HandleTongChengBookingError(tc.response)
			
			if tc.expectError {
				assert.Error(t, err)
				if tc.errorContains != "" {
					assert.Contains(t, err.Error(), tc.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
