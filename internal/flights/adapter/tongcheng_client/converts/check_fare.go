package converts

import (
	"errors"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func ToTongChengVerifyRequest(req *domain.SearchFlightsRequest, flight *domain.ResponseFlight) (*entities.VerifyRequest, error) {
	if req == nil || flight == nil {
		return nil, errors.New("invalid input")
	}

	verifyRequest := &entities.VerifyRequest{
		VerifyParams: &entities.VerifyParams{
			TripType:    FromSearchFlightsRequest(req),
			AdultNumber: req.Passengers.ADT,
			ChildNumber: req.Passengers.CHD,
		},
	}

	var solutionMetadata *domain.Metadata
	for _, md := range flight.Metadata {
		if md.Key == domain.MetaKeySolution {
			solutionMetadata = md
			break
		}
	}

	if solutionMetadata == nil {
		return nil, errors.New("metadata key 'solution' not found")
	}

	verifyRequest.Routing = solutionMetadata.Value.(*entities.Routing)

	return verifyRequest, nil
}

func ToTongChengSearchRefundRequest(dataID, tracingID string) (*entities.SearchRefundRequest, error) {
	if dataID == "" {
		return nil, errors.New("invalid input")
	}

	searchRefundRequest := &entities.SearchRefundRequest{
		SearchRefundParams: &entities.SearchRefundParams{
			DataID:  dataID,
			TraceID: tracingID,
		},
	}

	return searchRefundRequest, nil
}

func FromSearchFlightsRequest(req *domain.SearchFlightsRequest) enum.TripType {
	if req == nil || len(req.Itineraries) == 0 {
		return enum.TripTypeNone
	}

	if len(req.Itineraries) == 1 {
		return enum.TripTypeOneWay
	}

	if req.IsRoundTrip() {
		return enum.TripTypeRoundTrip
	}

	return enum.TripTypeNone
}
