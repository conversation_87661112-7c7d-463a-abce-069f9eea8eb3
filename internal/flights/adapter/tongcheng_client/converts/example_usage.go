package converts

import (
	"context"
	"fmt"
	"log"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

// ExampleBookingWorkflow minh họa cách sử dụng converter trong workflow booking hoàn chỉnh
func ExampleBookingWorkflow() {
	// 1. Chuẩn bị dữ liệu booking
	bookingDetails := &domain.BookingDetails{
		// ... populate booking details
	}
	
	pnr := &domain.PNR{
		SessionID: "session_123",
		// ... populate PNR data
	}

	// 2. Convert sang TongCheng booking request
	tcBookingRequest := ToTongChengBookingRequest(bookingDetails, pnr)
	if tcBookingRequest == nil {
		log.Fatal("Failed to create TongCheng booking request")
	}

	// 3. Gọi TongCheng API (giả lập)
	tcResponse := callTongChengBookingAPI(tcBookingRequest)
	
	// 4. Convert response về domain struct
	domainResponse, err := FromTongChengBookingResponse(tcResponse, tcBookingRequest.BookingParams.Routing)
	if err != nil {
		log.Fatalf("Failed to convert TongCheng response: %v", err)
	}

	// 5. Sử dụng domain response
	fmt.Printf("Booking created successfully: %s\n", domainResponse.BookingRef)
	fmt.Printf("PNR: %s\n", domainResponse.OrderNumRef)
	fmt.Printf("Last ticketing date: %d\n", domainResponse.LastTicketingDate)
	
	if domainResponse.ExpectedPrice != nil {
		fmt.Printf("Expected price: %.2f %s\n", 
			domainResponse.ExpectedPrice.Amount, 
			domainResponse.ExpectedPrice.Currency)
	}
}

// ExampleAdvancedBookingWorkflow minh họa cách sử dụng converter nâng cao
func ExampleAdvancedBookingWorkflow() {
	// 1. Chuẩn bị dữ liệu và options
	bookingDetails := &domain.BookingDetails{
		// ... populate booking details
	}
	
	pnr := &domain.PNR{
		SessionID: "session_456",
		// ... populate PNR data
	}

	// 2. Cấu hình options cho converter
	commissionRate := 0.03 // 3% commission
	options := &BookingConvertOptions{
		CommissionRate:         &commissionRate,
		DefaultCurrency:        "USD",
		SkipDefaultLastTktDate: false,
		FallbackLastTktHours:   36, // 36 giờ thay vì 24 giờ mặc định
		AirlineSystemOverride:  "TC_PREMIUM",
	}

	// 3. Convert sang TongCheng booking request
	tcBookingRequest := ToTongChengBookingRequest(bookingDetails, pnr)
	if tcBookingRequest == nil {
		log.Fatal("Failed to create TongCheng booking request")
	}

	// 4. Gọi TongCheng API
	tcResponse := callTongChengBookingAPI(tcBookingRequest)
	
	// 5. Xử lý error trước khi convert
	if err := HandleTongChengBookingError(tcResponse); err != nil {
		log.Fatalf("TongCheng booking error: %v", err)
	}

	// 6. Convert response với options nâng cao
	domainResponse, err := FromTongChengBookingResponseAdvanced(
		tcResponse, 
		tcBookingRequest.BookingParams.Routing, 
		options,
	)
	if err != nil {
		log.Fatalf("Failed to convert TongCheng response: %v", err)
	}

	// 7. Sử dụng domain response
	fmt.Printf("Advanced booking created: %s\n", domainResponse.BookingRef)
	fmt.Printf("Airline system: %s\n", domainResponse.AirlineSystem)
	
	if domainResponse.CommRate != nil {
		fmt.Printf("Commission rate: %.2f%%\n", *domainResponse.CommRate*100)
	}
}

// ExampleErrorHandling minh họa cách xử lý lỗi từ TongCheng
func ExampleErrorHandling() {
	// Giả lập response lỗi từ TongCheng
	tcResponse := &entities.BookingResponse{
		Response: entities.Response{
			IsSuccess: false,
			ErrorCode: "2002",
			ErrorMsg:  "Price has changed",
		},
	}

	// Xử lý lỗi chi tiết
	if err := HandleTongChengBookingError(tcResponse); err != nil {
		switch tcResponse.ErrorCode {
		case "2002":
			// Giá đã thay đổi - cần refresh giá
			fmt.Println("Price changed, need to refresh pricing")
			// Implement price refresh logic
			
		case "2001":
			// Chuyến bay không còn available
			fmt.Println("Flight no longer available")
			// Implement alternative flight search
			
		case "3001":
			// Thông tin hành khách không hợp lệ
			fmt.Println("Invalid passenger information")
			// Implement passenger data validation
			
		default:
			// Lỗi khác
			fmt.Printf("Booking failed: %v\n", err)
		}
		return
	}

	// Nếu không có lỗi, tiếp tục xử lý
	fmt.Println("Booking successful")
}

// ExampleTestingConverter minh họa cách test converter
func ExampleTestingConverter() {
	// Tạo mock data cho testing
	mockTcResponse := &entities.BookingResponse{
		Response: entities.Response{
			IsSuccess: true,
		},
		OrderNo:   "TC_TEST_123",
		PnrCode:   "TEST_PNR",
		GmtPayOut: "2024-12-15 14:30:00",
	}

	mockRouting := &entities.Routing{
		AdultPrice: 1200.0,
		ChildPrice: 600.0,
		AdultTax:   120.0,
		ChildTax:   60.0,
		Currency:   "CNY",
	}

	// Test basic converter
	result, err := FromTongChengBookingResponse(mockTcResponse, mockRouting)
	if err != nil {
		log.Fatalf("Test failed: %v", err)
	}

	// Verify results
	if result.BookingRef != "TC_TEST_123" {
		log.Fatalf("Expected BookingRef TC_TEST_123, got %s", result.BookingRef)
	}

	if result.ExpectedPrice == nil || result.ExpectedPrice.Amount != 1980.0 {
		log.Fatalf("Expected price 1980.0, got %v", result.ExpectedPrice)
	}

	fmt.Println("All tests passed!")
}

// ExampleIntegrationWithService minh họa cách tích hợp với service layer
func ExampleIntegrationWithService(ctx context.Context, bookingService BookingService) {
	// 1. Nhận request từ client
	bookingRequest := &domain.CreateBookingRequest{
		// ... populate from client request
	}

	// 2. Validate và prepare data
	bookingDetails, pnr, err := bookingService.PrepareBookingData(ctx, bookingRequest)
	if err != nil {
		log.Fatalf("Failed to prepare booking data: %v", err)
	}

	// 3. Convert sang TongCheng format
	tcRequest := ToTongChengBookingRequest(bookingDetails, pnr)
	
	// 4. Call TongCheng API
	tcResponse, err := bookingService.CallTongChengAPI(ctx, tcRequest)
	if err != nil {
		log.Fatalf("TongCheng API call failed: %v", err)
	}

	// 5. Handle TongCheng errors
	if err := HandleTongChengBookingError(tcResponse); err != nil {
		// Log error và return appropriate response
		log.Printf("TongCheng booking error: %v", err)
		// Return error to client with proper error code
		return
	}

	// 6. Convert response
	domainResponse, err := FromTongChengBookingResponse(tcResponse, tcRequest.BookingParams.Routing)
	if err != nil {
		log.Fatalf("Failed to convert response: %v", err)
	}

	// 7. Save to database và return response
	if err := bookingService.SaveBooking(ctx, domainResponse); err != nil {
		log.Fatalf("Failed to save booking: %v", err)
	}

	fmt.Printf("Booking completed successfully: %s\n", domainResponse.BookingRef)
}

// Mock interfaces và functions cho example
type BookingService interface {
	PrepareBookingData(ctx context.Context, req *domain.CreateBookingRequest) (*domain.BookingDetails, *domain.PNR, error)
	CallTongChengAPI(ctx context.Context, req *entities.BookingRequest) (*entities.BookingResponse, error)
	SaveBooking(ctx context.Context, response *domain.SvcCreateBookingResponse) error
}

// callTongChengBookingAPI là mock function cho việc gọi API
func callTongChengBookingAPI(request *entities.BookingRequest) *entities.BookingResponse {
	// Mock implementation
	return &entities.BookingResponse{
		Response: entities.Response{
			IsSuccess: true,
		},
		OrderNo:   "TC_MOCK_123456",
		PnrCode:   "MOCK_PNR",
		GmtPayOut: "2024-12-15 15:00:00",
	}
}
