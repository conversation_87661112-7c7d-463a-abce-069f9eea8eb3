package converts

import (
	"fmt"
	"time"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	domainEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func ToTongChengBookingRequest(bookingDetails *domain.BookingDetails, pnr *domain.PNR) *entities.BookingRequest {
	if bookingDetails == nil || pnr == nil {
		return nil
	}

	metadata := bookingDetails.Metadata
	var solution *entities.Routing
	for _, md := range metadata {
		if md.Key == domain.MetaKeySolution {
			solution = md.Value.(*entities.Routing)
			break
		}
	}
	if solution == nil {
		return nil
	}

	bookingRequest := &entities.BookingRequest{
		BookingParams: &entities.BookingParams{
			SessionID: pnr.SessionID,
			Routing:   solution,
			Passenger: toTongChengPassengers(pnr.ListPax),
			Contact:   toTongChengContact(pnr.ContactInfo),
		},
	}

	return bookingRequest
}

func toTongChengPassengers(paxInfos []*domain.PaxInfo) []*entities.Passenger {
	if len(paxInfos) == 0 {
		return nil
	}

	var passengers []*entities.Passenger
	for _, pax := range paxInfos {
		if pax == nil {
			continue
		}
		passenger := &entities.Passenger{
			FirstName:     pax.GivenName,
			LastName:      pax.Surname,
			PassengerType: toTongChengPassengerType(pax.Type),
			Gender:        toTongChengGender(pax.Gender),
		}
		if pax.DOB != nil {
			passenger.BirthDay = time.UnixMilli(*pax.DOB).Format("2006-01-02")
		}
		if pax.Passport != nil {
			passenger.CardNum = pax.Passport.Number
			passenger.CardType = enum.PassengerCardTypePassport
			passenger.Nationality = pax.Passport.IssuingCountry
			if pax.Passport.ExpiryDate > 0 {
				passenger.CardExpired = time.UnixMilli(pax.Passport.ExpiryDate).Format("2006-01-02")
			}
		}
		passengers = append(passengers, passenger)
	}
	return passengers
}

func toTongChengPassengerType(paxType domainEnum.PaxType) enum.PassengerType {
	switch paxType {
	case domainEnum.PaxTypeAdult:
		return enum.PassengerTypeADT
	case domainEnum.PaxTypeChildren:
		return enum.PassengerTypeCHD
	case domainEnum.PaxTypeInfant:
		return enum.PassengerTypeADT // TongCheng doesn't have infant type, use adult
	default:
		return enum.PassengerTypeADT
	}
}

func toTongChengGender(gender commonEnum.GenderType) enum.Gender {
	switch gender {
	case commonEnum.GenderTypeMale:
		return enum.Male
	case commonEnum.GenderTypeFeMale:
		return enum.Female
	default:
		return enum.Male
	}
}

func toTongChengContact(contact *domain.Contact) []*entities.Contact {
	if contact == nil {
		return nil
	}
	return []*entities.Contact{
		{
			Email:  contact.Email,
			Name:   fmt.Sprintf("%s/%s", contact.Surname, contact.GivenName),
			Mobile: contact.Phone,
		},
	}
}

func FromTongChengBookingResponse(tcResponse *entities.BookingResponse, originalRouting *entities.Routing) (*domain.SvcCreateBookingResponse, error) {
	if tcResponse == nil {
		return nil, fmt.Errorf("TongCheng booking response is nil")
	}

	// Kiểm tra response có thành công không
	if !tcResponse.IsSuccess {
		return nil, fmt.Errorf("TongCheng booking failed: %s - %s", tcResponse.ErrorCode, tcResponse.ErrorMsg)
	}

	response := &domain.SvcCreateBookingResponse{
		BookingRef:  tcResponse.OrderNo, // Sử dụng OrderNo làm booking reference
		OrderNumRef: tcResponse.PnrCode, // PNR code từ TongCheng
	}

	if tcResponse.GmtPayOut != "" {
		lastTktDate, err := parseTongChengPayoutDate(tcResponse.GmtPayOut)
		if err != nil {
			response.LastTicketingDate = 0
		} else {
			response.LastTicketingDate = lastTktDate
		}
	}

	return response, nil
}

// parseTongChengPayoutDate phân tích chuỗi thời gian GmtPayOut từ TongCheng
// Format thường là: "2024-12-11 11:55:00" hoặc tương tự
func parseTongChengPayoutDate(gmtPayOut string) (int64, error) {
	if gmtPayOut == "" {
		return 0, fmt.Errorf("empty payout date")
	}

	// Thử các format thời gian phổ biến của TongCheng
	formats := []string{
		"2006-01-02 15:04:05",     // Standard datetime format
		"2006-01-02T15:04:05",     // ISO format
		"2006-01-02T15:04:05Z",    // ISO with timezone
		"2006-01-02 15:04:05 MST", // With timezone
		"02/01/2006 15:04:05",     // Alternative format
	}

	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return 0, fmt.Errorf("load location error: %w", err)
	}

	for _, format := range formats {
		if t, err := time.ParseInLocation(format, gmtPayOut, loc); err == nil {
			return t.UnixMilli(), nil
		}
	}

	return 0, fmt.Errorf("unable to parse payout date: %s", gmtPayOut)
}
