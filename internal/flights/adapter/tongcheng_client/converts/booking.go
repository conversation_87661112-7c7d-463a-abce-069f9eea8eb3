package converts

import (
	"fmt"
	"strings"
	"time"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	domainEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

const (
	timeLayout = "2006-01-02 15:04:05"
)

func ToTongChengBookingRequest(bookingDetails *domain.BookingDetails, pnr *domain.PNR) *entities.BookingRequest {
	if bookingDetails == nil || pnr == nil {
		return nil
	}

	return &entities.BookingRequest{
		BookingParams: &entities.BookingParams{
			SessionID: pnr.SessionID,
			TripType:  toTongChengTripType(bookingDetails.FlightType),
			Routing:   toTongChengRouting(bookingDetails.FlightType, bookingDetails.Itineraries),
			Passenger: toTongChengPassengers(pnr.ListPax),
			Contact:   toTongChengContact(pnr.ContactInfo),
		},
	}
}

func toTongChengTripType(flightType domainEnum.FlightType) enum.TripType {
	switch flightType {
	case domainEnum.FlightTypeOneWay:
		return enum.TripTypeOneWay
	case domainEnum.FlightTypeRoundTrip:
		return enum.TripTypeRoundTrip
	default:
		return ""
	}
}

func toTongChengRouting(flightType domainEnum.FlightType, itineraries []*domain.FlightItinerary) *entities.Routing {
	if len(itineraries) == 0 {
		return nil
	}

	routing := &entities.Routing{}

	switch flightType {
	case domainEnum.FlightTypeRoundTrip:
		// Assumption: For RoundTrip, the first itinerary is outbound, the second is inbound.
		if len(itineraries) > 0 {
			routing.FromSegments = toTongChengSegments([]*domain.FlightItinerary{itineraries[0]})
		}
		if len(itineraries) > 1 {
			routing.RetSegments = toTongChengSegments([]*domain.FlightItinerary{itineraries[1]})
		}
	case domainEnum.FlightTypeOneWay:
		fallthrough
	default:
		// For OneWay or any other case, treat all as outbound segments.
		routing.FromSegments = toTongChengSegments(itineraries)
	}

	return routing
}

func toTongChengSegments(itineraries []*domain.FlightItinerary) []*entities.Segment {
	if len(itineraries) == 0 {
		return nil
	}

	var segments []*entities.Segment
	for _, itinerary := range itineraries {
		if itinerary == nil {
			continue
		}
		for _, seg := range itinerary.Segments {
			if seg == nil {
				continue
			}
			segments = append(segments, &entities.Segment{
				Arrival:       seg.ArrivalPlace,
				ArrivalDate:   time.UnixMilli(seg.ArrivalDate).Format(timeLayout),
				CabinGrade:    toTongChengCabinGrade(itinerary.CabinClass),
				Carrier:       seg.CarrierMarketing,
				Departure:     seg.DepartPlace,
				DepartureDate: time.UnixMilli(seg.DepartDate).Format(timeLayout),
				FlightNo:      seg.FlightNumber,
			})
		}
	}
	return segments
}

func toTongChengCabinGrade(cabinClass string) enum.CabinGrade {
	switch strings.ToUpper(cabinClass) {
	case "ECONOMY", "E":
		return enum.CabinGradeEconomy
	case "BUSINESS", "B":
		return enum.CabinGradeBusiness
	case "FIRST", "F":
		return enum.CabinGradeFirst
	default:
		return enum.CabinGradeEconomy
	}
}

func toTongChengPassengers(paxInfos []*domain.PaxInfo) []*entities.Passenger {
	if len(paxInfos) == 0 {
		return nil
	}

	var passengers []*entities.Passenger
	for _, pax := range paxInfos {
		if pax == nil {
			continue
		}
		passenger := &entities.Passenger{
			Name:          fmt.Sprintf("%s/%s", pax.Surname, pax.GivenName),
			PassengerType: toTongChengPassengerType(pax.Type),
			Phone:         pax.Phone,
			Sex:           toTongChengSex(pax.Gender),
		}
		if pax.DOB != nil {
			passenger.Birthday = time.UnixMilli(*pax.DOB).Format("2006-01-02")
		}
		if pax.Passport != nil {
			passenger.CertNo = pax.Passport.Number
			passenger.CertType = enum.CertTypePassport
			passenger.Country = pax.Passport.IssuingCountry
		}
		passengers = append(passengers, passenger)
	}
	return passengers
}

func toTongChengPassengerType(paxType domainEnum.PaxType) enum.PassengerType {
	switch paxType {
	case domainEnum.PaxTypeAdult:
		return enum.PassengerTypeAdult
	case domainEnum.PaxTypeChildren:
		return enum.PassengerTypeChild
	case domainEnum.PaxTypeInfant:
		return enum.PassengerTypeInfant
	default:
		return ""
	}
}

func toTongChengSex(gender commonEnum.GenderType) enum.Sex {
	switch gender {
	case commonEnum.GenderMale:
		return enum.SexMale
	case commonEnum.GenderFemale:
		return enum.SexFemale
	default:
		return ""
	}
}

func toTongChengContact(contact *domain.Contact) []*entities.Contact {
	if contact == nil {
		return nil
	}
	return []*entities.Contact{
		{
			Email: contact.Email,
			Name:  fmt.Sprintf("%s/%s", contact.Surname, contact.GivenName),
			Phone: contact.Phone,
		},
	}
}
