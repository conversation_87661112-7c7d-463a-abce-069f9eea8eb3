# TongCheng Booking Response Converter

## Tổng quan

Module này cung cấp cá<PERSON> hàm convert để chuyển đổi TongCheng booking response sang domain struct `SvcCreateBookingResponse` c<PERSON><PERSON> hệ thống.

## Cá<PERSON> hàm chính

### 1. FromTongChengBookingResponse (C<PERSON> bả<PERSON>)

```go
func FromTongChengBookingResponse(tcResponse *entities.BookingResponse, originalRouting *entities.Routing) (*domain.SvcCreateBookingResponse, error)
```

**Mục đích**: Chuyển đổi cơ bản từ TongCheng response sang domain struct.

**Tham số**:
- `tcResponse`: Response từ TongCheng booking API
- `originalRouting`: Thông tin routing gốc để tính toán ExpectedPrice

**Trả về**: 
- `*domain.SvcCreateBookingResponse`: Domain struct đã được mapping
- `error`: Lỗi nếu có

**V<PERSON> dụ sử dụng**:
```go
// Gọi <PERSON> booking API
tcResponse, err := tongchengClient.CreateBooking(bookingRequest)
if err != nil {
    return nil, err
}

// Convert sang domain struct
domainResponse, err := FromTongChengBookingResponse(tcResponse, originalRouting)
if err != nil {
    return nil, err
}
```

### 2. FromTongChengBookingResponseAdvanced (Nâng cao)

```go
func FromTongChengBookingResponseAdvanced(tcResponse *entities.BookingResponse, originalRouting *entities.Routing, options *BookingConvertOptions) (*domain.SvcCreateBookingResponse, error)
```

**Mục đích**: Chuyển đổi nâng cao với nhiều tùy chọn và validation chi tiết.

**Tham số**:
- `tcResponse`: Response từ TongCheng booking API
- `originalRouting`: Thông tin routing gốc
- `options`: Các tùy chọn convert (có thể nil)

**Ví dụ sử dụng**:
```go
options := &BookingConvertOptions{
    CommissionRate:         &commissionRate,
    DefaultCurrency:        "USD",
    SkipDefaultLastTktDate: false,
    FallbackLastTktHours:   48, // 48 giờ thay vì 24 giờ mặc định
    AirlineSystemOverride:  "TC_CUSTOM",
}

domainResponse, err := FromTongChengBookingResponseAdvanced(tcResponse, originalRouting, options)
```

## BookingConvertOptions

Struct chứa các tùy chọn cho việc convert:

```go
type BookingConvertOptions struct {
    CommissionRate         *float64 // Commission rate tùy chỉnh
    DefaultCurrency        string   // Currency mặc định nếu không có trong response
    SkipDefaultLastTktDate bool     // Có skip default last ticketing date không
    FallbackLastTktHours   int      // Số giờ fallback cho last ticketing date (mặc định 24h)
    AirlineSystemOverride  string   // Override airline system identifier
}
```

## Mapping Logic

### 1. BookingRef và OrderNumRef
- `BookingRef`: Sử dụng `tcResponse.OrderNo`
- `OrderNumRef`: Sử dụng `tcResponse.PnrCode`

### 2. LastTicketingDate
- Ưu tiên parse từ `tcResponse.GmtPayOut`
- Fallback: Thời gian hiện tại + số giờ fallback (mặc định 24h)
- Hỗ trợ nhiều format thời gian phổ biến

### 3. ExpectedPrice
- Tính từ `routing.AdultPrice + routing.ChildPrice + routing.AdultTax + routing.ChildTax`
- Currency ưu tiên từ routing, fallback là "CNY"
- Validation: Chỉ tạo nếu totalAmount > 0

### 4. AirlineSystem
- Mặc định: "TC" (TongCheng identifier)
- Có thể override qua options

### 5. Các field khác
- `FareExpiredDate`: 0 (TongCheng không cung cấp)
- `TicketExpiredDate`: 0 (TongCheng không cung cấp)
- `CommRate`: nil hoặc từ options
- `SeatError`: nil (không có trong booking response)

## Error Handling

### HandleTongChengBookingError

```go
func HandleTongChengBookingError(tcResponse *entities.BookingResponse) error
```

Xử lý và mapping các error code phổ biến của TongCheng:
- `1001`: Invalid request parameters
- `1002`: Authentication failed
- `2001`: Flight not available
- `2002`: Price changed
- `3001`: Passenger information invalid
- `4001`: Payment failed
- `5001`: System error

## Best Practices

### 1. Validation
- Luôn kiểm tra `tcResponse.IsSuccess` trước khi convert
- Sử dụng `validateTongChengBookingResponse` cho validation chi tiết

### 2. Error Handling
- Sử dụng `HandleTongChengBookingError` để xử lý lỗi từ TongCheng
- Log chi tiết để debug khi cần

### 3. Performance
- Sử dụng hàm cơ bản nếu không cần tùy chọn nâng cao
- Cache routing information nếu có thể

### 4. Testing
```go
func TestFromTongChengBookingResponse(t *testing.T) {
    // Mock TongCheng response
    tcResponse := &entities.BookingResponse{
        Response: entities.Response{
            IsSuccess: true,
        },
        OrderNo:   "TC123456",
        PnrCode:   "ABC123",
        GmtPayOut: "2024-12-11 11:55:00",
    }
    
    // Mock routing
    routing := &entities.Routing{
        AdultPrice: 1000.0,
        ChildPrice: 500.0,
        AdultTax:   100.0,
        ChildTax:   50.0,
        Currency:   "CNY",
    }
    
    // Test convert
    result, err := FromTongChengBookingResponse(tcResponse, routing)
    assert.NoError(t, err)
    assert.Equal(t, "TC123456", result.BookingRef)
    assert.Equal(t, "ABC123", result.OrderNumRef)
    assert.Equal(t, 1650.0, result.ExpectedPrice.Amount)
}
```

## Troubleshooting

### 1. Parse Date Error
- Kiểm tra format của `GmtPayOut`
- Thêm format mới vào `parseTongChengPayoutDate` nếu cần

### 2. Missing ExpectedPrice
- Kiểm tra routing có giá không
- Verify currency mapping

### 3. Validation Error
- Kiểm tra `OrderNo` và `PnrCode` có trong response không
- Verify `IsSuccess` flag
