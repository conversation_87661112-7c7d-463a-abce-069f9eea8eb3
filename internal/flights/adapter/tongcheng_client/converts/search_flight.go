package converts

import (
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	flightEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func ToSearchFlightRequest(in *domain.SearchFlightsRequest, class enum.CabinGrade) *entities.SearchFlightRequest {
	if in == nil {
		return nil
	}

	return &entities.SearchFlightRequest{
		SearchParams: &entities.SearchParams{
			TripType:   ToSearchTripTypeRequest(in),
			FromCity:   ToSearchCityRequest(in, in.Itineraries[0].DepartPlace),
			ToCity:     ToSearchCityRequest(in, in.Itineraries[0].ArrivalPlace),
			CabinGrade: []enum.CabinGrade{class},
			AdultNum:   in.Passengers.ADT,
			ChildNum:   in.Passengers.CHD,
			FromDate:   ToSearchDateRequest(in.Itineraries[0]),
			RetDate:    ToSearchDateRequest(in.Itineraries[1]),
		},
	}
}

func ToSearchTripTypeRequest(in *domain.SearchFlightsRequest) enum.TripType {
	if in == nil {
		return enum.TripTypeNone
	}

	if in.IsRoundTrip() {
		return enum.TripTypeRoundTrip
	}

	return enum.TripTypeOneWay
}

func ToSearchCityRequest(in *domain.SearchFlightsRequest, code string) string {
	if in == nil {
		return ""
	}

	airport, ok := in.AirportMap[code]
	if !ok {
		return ""
	}

	return airport.City
}

func ToSearchDateRequest(iti *domain.ItineraryRequest) string {
	if iti == nil {
		return ""
	}

	switch {
	case iti.DepartDate > 0:
		return time.UnixMilli(iti.DepartDate).Format(constants.DateFormatYMD)
	case iti.DepartDateStr != "":
		if t, err := time.Parse(constants.DateFormatDMYDash, iti.DepartDateStr); err == nil {
			return t.Format(constants.DateFormatYMD)
		}
	}

	return ""
}

func ToDomainResponseFlights(in *entities.SearchFlightResponse, searchReq *domain.SearchFlightsRequest) ([]*domain.ResponseFlight, error) {
	requestAirports := lo.Map(searchReq.Itineraries, func(itinerary *domain.ItineraryRequest, _ int) string {
		return fmt.Sprintf("%s-%s", itinerary.DepartPlace, itinerary.ArrivalPlace)
	})

	return lo.FilterMap(in.Routings, func(flight *entities.Routing, _ int) (*domain.ResponseFlight, bool) {
		recFlight, err := ToDomainResponseFlight(flight, searchReq)
		if err != nil {
			return nil, false
		}

		for _, iti := range recFlight.Itineraries {
			if !lo.Contains(requestAirports, fmt.Sprintf("%s-%s", iti.DepartPlace, iti.ArrivalPlace)) ||
				iti.FlightNumber == "" || iti.FlightNumber == "null" {
				return nil, false
			}
		}

		return recFlight, true
	}), nil
}

func ToDomainResponseFlight(flight *entities.Routing, searchReq *domain.SearchFlightsRequest) (*domain.ResponseFlight, error) {
	if flight == nil {
		return nil, fmt.Errorf("flight is nil")
	}

	out := &domain.ResponseFlight{
		FlightID:            helpers.GenerateFlightID(flightEnum.FlightProviderTongCheng),
		SearchTotalFareInfo: ToDomainSearchTotalFareInfo(flight, searchReq),
		Provider:            flightEnum.FlightProviderTongCheng,
		Metadata: []*domain.Metadata{
			{
				Key:   "routing",
				Value: flight,
			},
		},
		OptionType: flightEnum.FlightOptionTypeRecommend,
	}

	var itineraries []*domain.FlightItinerary

	for idx, segs := range [][]*entities.Segment{flight.FromSegment, flight.RetSegment} {
		iti, err := toDomainFlightItinerary(segs, flight, idx+1)
		if err != nil {
			return nil, err
		}

		itineraries = append(itineraries, iti)
	}

	out.Itineraries = itineraries

	return out, nil
}

func ToDomainSearchTotalFareInfo(flight *entities.Routing, searchReq *domain.SearchFlightsRequest) domain.SearchTotalFareInfo {
	var (
		totalFareBasic float64
		totalTaxAmount float64
		paxes          []*domain.ItineraryPaxFare
	)

	addPax := func(paxType flightEnum.PaxType, totalPrice, totalTax float64, paxCount int) {
		if paxCount <= 0 {
			return
		}

		fareBasic := totalPrice / float64(paxCount)
		taxAmount := totalTax / float64(paxCount)
		paxes = append(paxes, &domain.ItineraryPaxFare{
			PaxType:    paxType,
			FareAmount: fareBasic + taxAmount,
			FareBasic:  fareBasic,
			TaxAmount:  taxAmount,
			Currency:   flight.Currency,
		})
	}

	// Tính cho ADT và CHD
	addPax(flightEnum.PaxTypeAdult, flight.AdultPrice, flight.AdultTax, searchReq.Passengers.ADT)
	addPax(flightEnum.PaxTypeChildren, flight.ChildPrice, flight.ChildTax, searchReq.Passengers.CHD)

	totalFareBasic = flight.AdultPrice + flight.ChildPrice
	totalTaxAmount = flight.AdultTax + flight.ChildTax
	totalFareAmount := totalFareBasic + totalTaxAmount

	return domain.SearchTotalFareInfo{
		TotalPaxFares:       paxes,
		BaseTotalFareAmount: totalFareAmount,
		TotalFareAmount:     totalFareAmount,
		TotalFareBasic:      totalFareBasic,
		TotalTaxAmount:      totalTaxAmount,
		Currency:            flight.Currency,
	}
}

func toDomainFlightItinerary(in []*entities.Segment, flight *entities.Routing, idx int) (*domain.FlightItinerary, error) {
	if len(in) == 0 || flight == nil {
		return nil, fmt.Errorf("no segments or flight data provided")
	}

	segmentIndex := in[0].SegmentIndex
	bookingKey := flight.Data

	fareBasics := []string{}
	// split it by '/' to get individual fare basis codes
	if flight.FareBasis != "" {
		fareBasics = strings.Split(flight.FareBasis, "/")
	}

	segments := []*domain.ItinerarySegment{}
	for index, seg := range in {
		fareBasis := ""
		if index < len(fareBasics) {
			fareBasis = fareBasics[index]
		}

		itiSeg, err := toDomainItinerarySegment(seg, fareBasis, index+1)
		if err != nil {
			return nil, err
		}

		segments = append(segments, itiSeg)
	}

	firstSeg := segments[0]
	lastSeg := segments[len(segments)-1]

	avai := firstSeg.Availability
	if avai > 9 {
		avai = 9
	}

	out := &domain.FlightItinerary{
		Index:              idx,
		FareBasis:          firstSeg.FareBasis,
		CabinClass:         firstSeg.CabbinClass,
		CabinClassCode:     firstSeg.CabinClassCode,
		BookingClass:       firstSeg.BookingClass,
		Availability:       avai,
		DepartPlace:        firstSeg.DepartPlace,
		DepartDate:         firstSeg.DepartDate,
		ArrivalPlace:       lastSeg.ArrivalPlace,
		ArrivalDate:        lastSeg.ArrivalDate,
		CarrierMarketing:   firstSeg.CarrierMarketing,
		CarrierOperator:    firstSeg.CarrierOperator,
		FlightNumber:       firstSeg.FlightNumber,
		StopNumber:         len(segments) - 1,
		FreeBaggage:        getItiFreeBaggs(flight.Rule, segmentIndex),
		ProviderBookingKey: bookingKey,
		Segments:           segments,
	}

	out.GenerateItiID()

	return out, nil
}

func toDomainItinerarySegment(segment *entities.Segment, fareBasis string, idx int) (*domain.ItinerarySegment, error) {
	if segment == nil {
		return nil, fmt.Errorf("segment is nil")
	}

	departDt, err := utils.DateStringToUnixMilli(segment.DepTime, "")
	if err != nil {
		return nil, errors.Wrap(err, "invalid depart date "+segment.DepTime)
	}

	arrivalDt, err := utils.DateStringToUnixMilli(segment.ArrTime, "")
	if err != nil {
		return nil, errors.Wrap(err, "invalid arrival date "+segment.ArrTime)
	}

	return &domain.ItinerarySegment{
		Index:            idx,
		DepartPlace:      segment.DepAirport,
		DepartDate:       departDt,
		DepartTerminal:   segment.DepTerminal,
		ArrivalPlace:     segment.ArrAirport,
		ArrivalDate:      arrivalDt,
		ArrivalTerminal:  segment.ArrTerminal,
		CarrierMarketing: segment.Carrier,
		CarrierOperator:  segment.OperatingCarrier,
		FlightNumber:     segment.FlightNumber,
		Aircraft:         constants.GetAirCraftName(segment.AircraftCode),
		BookingClass:     segment.Cabin,
		CabinClassCode:   string(segment.CabinGrade),
		FareBasis:        fareBasis,
		FlightDuration:   segment.FlightDuration,
		Availability:     int(segment.CabinCount),
		CabbinClass:      enum.CabinGradeMap[segment.CabinGrade],
	}, nil

}

func getItiFreeBaggs(rule *entities.Rule, segIdx int32) []*domain.BaggageInfo {
	if rule == nil || rule.BaggageInfo == nil || len(rule.BaggageInfo.BaggageRules) == 0 {
		return nil
	}

	out := toDomainBaggageRules(rule.BaggageInfo.BaggageRules, segIdx)

	return out
}

func getBagWeight(piece int32) string {
	if piece == 1 {
		return fmt.Sprintf("%d Piece", piece)
	} else {
		return fmt.Sprintf("%d Pieces", piece)
	}
}

func toDomainBaggageRules(baggageRules []*entities.BaggageRule, segIdx int32) []*domain.BaggageInfo {
	var out []*domain.BaggageInfo

	for _, baggageRule := range baggageRules {
		if baggageRule.SegmentNum != segIdx {
			continue
		}

		baggageInfo := toDomainBaggageRule(baggageRule)
		if baggageInfo != nil {
			out = append(out, baggageInfo)
		}
	}

	return out
}

func toDomainBaggageRule(baggageRule *entities.BaggageRule) *domain.BaggageInfo {
	if baggageRule == nil {
		return nil
	}

	return &domain.BaggageInfo{
		Name:          getBagWeight(baggageRule.BaggagePieces),
		Quantity:      int64(baggageRule.BaggageAllowance),
		PaxType:       flightEnum.PaxTypeMap[int(baggageRule.PassengerType)],
		IsHandBaggage: baggageRule.BaggageType != 0,
	}
}
