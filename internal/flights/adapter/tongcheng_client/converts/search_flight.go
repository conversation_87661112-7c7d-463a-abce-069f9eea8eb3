package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func ToSearchFlightRequest(in *domain.SearchFlightsRequest, class enum.CabinGrade) *entities.SearchFlightRequest {
	if in == nil {
		return nil
	}

	return &entities.SearchFlightRequest{}
}

func ToDomainResponseFlights(in *entities.SearchFlightResponse, searchReq *domain.SearchFlightsRequest) ([]*domain.ResponseFlight, error) {
	return nil, nil
}
