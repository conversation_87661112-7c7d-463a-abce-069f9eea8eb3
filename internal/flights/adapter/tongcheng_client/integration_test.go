// internal/flights/adapter/tongcheng_client/integration_test.go
package tongcheng_client

import (
	"context"
	"encoding/json"
	"testing"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

func TestTongchengAdapter_CheckFare_Integration(t *testing.T) {
	cfg := &config.Schema{
		TongChengBaseURL:    "xxx",
		TongChengPartnerKey: "xxx",
		TongChengUsername:   "xxx",
		TongChengPartnerID:  "xxx",
		ProxyURL:            "xxx",
	}

	tongchengClient := client.NewTongChengClient(cfg, nil)
	adapter := &tongchengAdapter{
		cfg:    cfg,
		client: tongchengClient,
	}

	req := &domain.SearchFlightsRequest{
		Passengers: domain.PaxRequest{ADT: 1, CHD: 1},
		Itineraries: []*domain.ItineraryRequest{
			{
				DepartPlace:  "CAN",
				DepartDate:   1712764800,
				ArrivalPlace: "BKK",
			},
			{
				DepartPlace:  "BKK",
				DepartDate:   1713369600,
				ArrivalPlace: "CAN",
			},
		},
	}

	jsonData := []byte(`{
      "adultPrice": 1353,
      "adultTax": 1076,
      "childPrice": 1095,
      "childPublishPrice": 1095,
      "childTax": 865,
      "currency": "CNY",
      "data": "91ed112d769611f099a06141d8e4287c$&$165",
      "fareBasis": "E1ABDB0T/E1ABDB0T/E1ABDB0T/E1ABDB0T",
      "fromSegments": [
        {
          "aircraftCode": "321",
          "arrAirport": "TFU",
          "arrAirportName": "成都天府国际机场",
          "arrCity": "CTU",
          "arrCityName": "成都",
          "arrTerminal": "T2",
          "arrTime": "202512100850",
          "cabin": "B",
          "cabinCount": 9,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "CAN",
          "depAirportName": "广州白云国际机场",
          "depCity": "CAN",
          "depCityName": "广州市",
          "depTerminal": "T2",
          "depTime": "202512100625",
          "duration": 145,
          "equip": {
            "craftCode": "321",
            "craftName": "空客A321"
          },
          "flightNumber": "3U6716",
          "segmentIndex": 1,
          "segmentType": 1,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        },
        {
          "aircraftCode": "320",
          "arrAirport": "BKK",
          "arrAirportName": "素万那普国际机场",
          "arrCity": "BKK",
          "arrCityName": "曼谷",
          "arrTime": "202512101720",
          "cabin": "E",
          "cabinCount": 8,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "TFU",
          "depAirportName": "成都天府国际机场",
          "depCity": "CTU",
          "depCityName": "成都",
          "depTerminal": "T1",
          "depTime": "202512101505",
          "duration": 195,
          "equip": {
            "craftCode": "320",
            "craftName": "空客A320"
          },
          "flightNumber": "3U3935",
          "segmentIndex": 2,
          "segmentType": 1,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        }
      ],
      "groupQualification": 0,
      "maxPassengerCount": 9,
      "minPassengerCount": 1,
      "nationalityType": 0,
      "noCertificateOrder": 0,
      "passengerQualification": 0,
      "productCode": 0,
      "productType": 1,
      "publishPrice": 1353,
      "resouceCategory": 0,
      "retSegments": [
        {
          "aircraftCode": "320",
          "arrAirport": "TFU",
          "arrAirportName": "成都天府国际机场",
          "arrCity": "CTU",
          "arrCityName": "成都",
          "arrTerminal": "T1",
          "arrTime": "202512151610",
          "cabin": "E",
          "cabinCount": 9,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "BKK",
          "depAirportName": "素万那普国际机场",
          "depCity": "BKK",
          "depCityName": "曼谷",
          "depTime": "202512151210",
          "duration": 180,
          "equip": {
            "craftCode": "320",
            "craftName": "空客A320"
          },
          "flightNumber": "3U3938",
          "segmentIndex": 3,
          "segmentType": 2,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        },
        {
          "aircraftCode": "321",
          "arrAirport": "CAN",
          "arrAirportName": "广州白云国际机场",
          "arrCity": "CAN",
          "arrCityName": "广州市",
          "arrTerminal": "T2",
          "arrTime": "202512152145",
          "cabin": "B",
          "cabinCount": 9,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "TFU",
          "depAirportName": "成都天府国际机场",
          "depCity": "CTU",
          "depCityName": "成都",
          "depTerminal": "T2",
          "depTime": "202512151930",
          "duration": 135,
          "equip": {
            "craftCode": "321",
            "craftName": "空客A321"
          },
          "flightNumber": "3U6715",
          "segmentIndex": 4,
          "segmentType": 2,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        }
      ],
      "rule": {
        "baggageInfo": {
          "baggageRules": [
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 1
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 1
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 2
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 2
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 3
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 3
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 4
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 4
            }
          ],
          "hasBaggage": 1
        },
        "changeInfos": [
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 0,
            "currency": "CNY",
            "passengerType": 0
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 1,
            "currency": "CNY",
            "passengerType": 0
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 0,
            "currency": "CNY",
            "passengerType": 1
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 1,
            "currency": "CNY",
            "passengerType": 1
          }
        ],
        "refundInfos": [
          {
            "currency": "CNY",
            "passengerType": 0,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 0,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 0,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 1,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 1,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 0,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 1,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 1,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          }
        ]
      },
      "ticketInvoiceType": 0,
      "ticketTimeLimit": 0,
      "ticketTimeType": 1,
      "ticketType": 0,
      "validatingCarrier": "3U",
      "visaLimitType": 0
    }`)

	routing := &entities.Routing{}
	_ = json.Unmarshal(jsonData, routing)

	flight := &domain.ResponseFlight{
		Metadata: []*domain.Metadata{
			{
				Key:   domain.MetaKeySolution,
				Value: routing,
			},
		},
	}
	tracingID := "integration-test-trace-id"

	_, _ = adapter.CheckFare(context.Background(), req, flight, tracingID)

}
