// internal/flights/adapter/tongcheng_client/integration_test.go
package tongcheng_client

import (
	"context"
	"encoding/json"
	"testing"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

func TestTongchengAdapter_CheckFare_Integration(t *testing.T) {
	cfg := &config.Schema{
		TongChengBaseURL:    "xxx",
		TongChengPartnerKey: "xxx",
		TongChengUsername:   "xxx",
		TongChengPartnerID:  "xxx",
		ProxyURL:            "xxx",
	}

	tongchengClient := client.NewTongChengClient(cfg, nil)
	adapter := &tongchengAdapter{
		cfg:    cfg,
		client: tongchengClient,
	}

	req := &domain.SearchFlightsRequest{
		Passengers: domain.PaxRequest{ADT: 1, CHD: 1},
		Itineraries: []*domain.ItineraryRequest{
			{
				DepartPlace:  "CAN",
				DepartDate:   1712764800,
				ArrivalPlace: "BKK",
			},
			{
				DepartPlace:  "BKK",
				DepartDate:   1713369600,
				ArrivalPlace: "CAN",
			},
		},
	}

	jsonData := []byte(`{
      "adultPrice": 1353,
      "adultTax": 1076,
      "childPrice": 1095,
      "childPublishPrice": 1095,
      "childTax": 865,
      "currency": "CNY",
      "data": "91ed112d769611f099a06141d8e4287c$&$165",
      "fareBasis": "E1ABDB0T/E1ABDB0T/E1ABDB0T/E1ABDB0T",
      "fromSegments": [
        {
          "aircraftCode": "321",
          "arrAirport": "TFU",
          "arrAirportName": "成都天府国际机场",
          "arrCity": "CTU",
          "arrCityName": "成都",
          "arrTerminal": "T2",
          "arrTime": "************",
          "cabin": "B",
          "cabinCount": 9,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "CAN",
          "depAirportName": "广州白云国际机场",
          "depCity": "CAN",
          "depCityName": "广州市",
          "depTerminal": "T2",
          "depTime": "************",
          "duration": 145,
          "equip": {
            "craftCode": "321",
            "craftName": "空客A321"
          },
          "flightNumber": "3U6716",
          "segmentIndex": 1,
          "segmentType": 1,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        },
        {
          "aircraftCode": "320",
          "arrAirport": "BKK",
          "arrAirportName": "素万那普国际机场",
          "arrCity": "BKK",
          "arrCityName": "曼谷",
          "arrTime": "202512101720",
          "cabin": "E",
          "cabinCount": 8,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "TFU",
          "depAirportName": "成都天府国际机场",
          "depCity": "CTU",
          "depCityName": "成都",
          "depTerminal": "T1",
          "depTime": "202512101505",
          "duration": 195,
          "equip": {
            "craftCode": "320",
            "craftName": "空客A320"
          },
          "flightNumber": "3U3935",
          "segmentIndex": 2,
          "segmentType": 1,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        }
      ],
      "groupQualification": 0,
      "maxPassengerCount": 9,
      "minPassengerCount": 1,
      "nationalityType": 0,
      "noCertificateOrder": 0,
      "passengerQualification": 0,
      "productCode": 0,
      "productType": 1,
      "publishPrice": 1353,
      "resouceCategory": 0,
      "retSegments": [
        {
          "aircraftCode": "320",
          "arrAirport": "TFU",
          "arrAirportName": "成都天府国际机场",
          "arrCity": "CTU",
          "arrCityName": "成都",
          "arrTerminal": "T1",
          "arrTime": "202512151610",
          "cabin": "E",
          "cabinCount": 9,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "BKK",
          "depAirportName": "素万那普国际机场",
          "depCity": "BKK",
          "depCityName": "曼谷",
          "depTime": "************",
          "duration": 180,
          "equip": {
            "craftCode": "320",
            "craftName": "空客A320"
          },
          "flightNumber": "3U3938",
          "segmentIndex": 3,
          "segmentType": 2,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        },
        {
          "aircraftCode": "321",
          "arrAirport": "CAN",
          "arrAirportName": "广州白云国际机场",
          "arrCity": "CAN",
          "arrCityName": "广州市",
          "arrTerminal": "T2",
          "arrTime": "************",
          "cabin": "B",
          "cabinCount": 9,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "TFU",
          "depAirportName": "成都天府国际机场",
          "depCity": "CTU",
          "depCityName": "成都",
          "depTerminal": "T2",
          "depTime": "202512151930",
          "duration": 135,
          "equip": {
            "craftCode": "321",
            "craftName": "空客A321"
          },
          "flightNumber": "3U6715",
          "segmentIndex": 4,
          "segmentType": 2,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        }
      ],
      "rule": {
        "baggageInfo": {
          "baggageRules": [
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 1
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 1
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 2
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 2
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 3
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 3
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 4
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 4
            }
          ],
          "hasBaggage": 1
        },
        "changeInfos": [
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 0,
            "currency": "CNY",
            "passengerType": 0
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 1,
            "currency": "CNY",
            "passengerType": 0
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 0,
            "currency": "CNY",
            "passengerType": 1
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 1,
            "currency": "CNY",
            "passengerType": 1
          }
        ],
        "refundInfos": [
          {
            "currency": "CNY",
            "passengerType": 0,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 0,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 0,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 1,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 1,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 0,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 1,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 1,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          }
        ]
      },
      "ticketInvoiceType": 0,
      "ticketTimeLimit": 0,
      "ticketTimeType": 1,
      "ticketType": 0,
      "validatingCarrier": "3U",
      "visaLimitType": 0
    }`)

	routing := &entities.Routing{}
	_ = json.Unmarshal(jsonData, routing)

	flight := &domain.ResponseFlight{
		Metadata: []*domain.Metadata{
			{
				Key:   domain.MetaKeySolution,
				Value: routing,
			},
		},
	}
	tracingID := "integration-test-trace-id"

	_, _ = adapter.CheckFare(context.Background(), req, flight, tracingID)

}

func TestTongchengAdapter_CreateBooking_Integration(t *testing.T) {
	cfg := &config.Schema{
		TongChengBaseURL:    "xxx",
		TongChengPartnerKey: "xxx",
		TongChengUsername:   "xxx",
		TongChengPartnerID:  "xxx",
		ProxyURL:            "xxx",
	}

	tongchengClient := client.NewTongChengClient(cfg, nil)
	adapter := &tongchengAdapter{
		cfg:    cfg,
		client: tongchengClient,
	}

	// Tạo mock routing data từ CheckFare response
	routingJSON := []byte(`{
		"adultPrice": 1353,
		"adultTax": 1076,
		"childPrice": 1095,
		"childTax": 865,
		"currency": "CNY",
		"data": "91ed112d769611f099a06141d8e4287c$&$165",
		"fareBasis": "E1ABDB0T/E1ABDB0T/E1ABDB0T/E1ABDB0T",
		"fromSegments": [
			{
				"aircraftCode": "321",
				"arrAirport": "TFU",
				"arrTime": "************",
				"cabin": "B",
				"cabinGrade": "Y",
				"carrier": "3U",
				"depAirport": "CAN",
				"depTime": "************",
				"flightNumber": "3U6716"
			}
		],
		"retSegments": [
			{
				"aircraftCode": "320",
				"arrAirport": "CAN",
				"arrTime": "************",
				"cabin": "B",
				"cabinGrade": "Y",
				"carrier": "3U",
				"depAirport": "BKK",
				"depTime": "************",
				"flightNumber": "3U3938"
			}
		]
	}`)

	routing := &entities.Routing{}
	_ = json.Unmarshal(routingJSON, routing)

	// Tạo BookingDetails với metadata chứa routing
	bookingDetails := &domain.BookingDetails{
		OfficeID:    "TEST_OFFICE",
		BookingCode: "TEST_BOOKING_001",
		FlightType:  enum.FlightTypeInternational,
		Itineraries: []*domain.FlightItinerary{
			{
				DepartPlace:  "CAN",
				ArrivalPlace: "BKK",
				CabinClass:   "Y",
				Segments: []*domain.ItinerarySegment{
					{
						DepartPlace:      "CAN",
						ArrivalPlace:     "TFU",
						DepartDate:       *************, // 2024-12-10 06:25
						ArrivalDate:      *************, // 2024-12-10 08:50
						FlightNumber:     "3U6716",
						CarrierMarketing: "3U",
						CarrierOperator:  "3U",
						Aircraft:         "321",
					},
				},
			},
			{
				DepartPlace:  "BKK",
				ArrivalPlace: "CAN",
				CabinClass:   "Y",
				Segments: []*domain.ItinerarySegment{
					{
						DepartPlace:      "BKK",
						ArrivalPlace:     "CAN",
						DepartDate:       *************, // 2024-12-15 12:10
						ArrivalDate:      *************, // 2024-12-15 21:45
						FlightNumber:     "3U3938",
						CarrierMarketing: "3U",
						CarrierOperator:  "3U",
						Aircraft:         "320",
					},
				},
			},
		},
		Metadata: []*domain.Metadata{
			{
				Key:   domain.MetaKeySolution,
				Value: routing,
			},
		},
	}

	// Tạo PNR với thông tin hành khách và liên hệ
	pnr := &domain.PNR{
		SessionID: "test-session-12345",
		ListPax: []*domain.PaxInfo{
			{
				ID:        1,
				Type:      enum.PaxTypeAdult,
				Surname:   "NGUYEN",
				GivenName: "VAN A",
				Gender:    1,                                                       // commonEnum.GenderTypeMale
				DOB:       func() *int64 { t := int64(************); return &t }(), // 1990-01-01
				Passport: &domain.PaxPassport{
					Number:         "A12345678",
					IssuingCountry: "VN",
					ExpiryDate:     *************, // 2030-01-01
				},
			},
			{
				ID:        2,
				Type:      enum.PaxTypeChildren,
				Surname:   "NGUYEN",
				GivenName: "THI B",
				Gender:    2,                                                        // commonEnum.GenderTypeFeMale
				DOB:       func() *int64 { t := int64(*************); return &t }(), // 2015-01-01
				Passport: &domain.PaxPassport{
					Number:         "B87654321",
					IssuingCountry: "VN",
					ExpiryDate:     *************, // 2030-01-01
				},
			},
		},
		ContactInfo: &domain.Contact{
			Surname:   "NGUYEN",
			GivenName: "VAN A",
			Phone:     "84901234567",
			Email:     "<EMAIL>",
			Gender:    1, // commonEnum.GenderTypeMale
		},
	}

	tracingID := "integration-test-create-booking"

	// Gọi CreateBooking method
	result, err := adapter.CreateBooking(context.Background(), bookingDetails, pnr, tracingID)

	// Kiểm tra kết quả
	if err != nil {
		t.Logf("CreateBooking returned error: %v", err)
		// Trong integration test thực tế, có thể expect error do không có credentials thật
		return
	}

	if result != nil {
		t.Logf("CreateBooking successful:")
		t.Logf("  BookingRef: %s", result.BookingRef)
		t.Logf("  OrderNumRef: %s", result.OrderNumRef)
		t.Logf("  LastTicketingDate: %d", result.LastTicketingDate)
		t.Logf("  AirlineSystem: %s", result.AirlineSystem)

		if result.ExpectedPrice != nil {
			t.Logf("  ExpectedPrice: %.2f %s", result.ExpectedPrice.Amount, result.ExpectedPrice.Currency)
		}

		// Validate response fields
		if result.BookingRef == "" {
			t.Error("Expected BookingRef to be non-empty")
		}
		if result.OrderNumRef == "" {
			t.Error("Expected OrderNumRef to be non-empty")
		}
		if result.AirlineSystem == "" {
			t.Error("Expected AirlineSystem to be non-empty")
		}
	} else {
		t.Log("CreateBooking returned nil result")
	}
}

func TestTongchengAdapter_CreateBooking_OneWay_Integration(t *testing.T) {
	cfg := &config.Schema{
		TongChengBaseURL:    "xxx",
		TongChengPartnerKey: "xxx",
		TongChengUsername:   "xxx",
		TongChengPartnerID:  "xxx",
		ProxyURL:            "xxx",
	}

	tongchengClient := client.NewTongChengClient(cfg, nil)
	adapter := &tongchengAdapter{
		cfg:    cfg,
		client: tongchengClient,
	}

	// Tạo mock routing data cho one-way trip
	routingJSON := []byte(`{
		"adultPrice": 800,
		"adultTax": 200,
		"childPrice": 0,
		"childTax": 0,
		"currency": "CNY",
		"data": "oneway123$&$100",
		"fareBasis": "E1ABDB0T",
		"fromSegments": [
			{
				"aircraftCode": "737",
				"arrAirport": "BKK",
				"arrTime": "************",
				"cabin": "B",
				"cabinGrade": "Y",
				"carrier": "VJ",
				"depAirport": "SGN",
				"depTime": "************",
				"flightNumber": "VJ815"
			}
		]
	}`)

	routing := &entities.Routing{}
	_ = json.Unmarshal(routingJSON, routing)

	// Tạo BookingDetails cho one-way trip
	bookingDetails := &domain.BookingDetails{
		OfficeID:    "TEST_OFFICE_OW",
		BookingCode: "TEST_ONEWAY_001",
		FlightType:  enum.FlightTypeInternational,
		Itineraries: []*domain.FlightItinerary{
			{
				DepartPlace:  "SGN",
				ArrivalPlace: "BKK",
				CabinClass:   "Y",
				Segments: []*domain.ItinerarySegment{
					{
						DepartPlace:      "SGN",
						ArrivalPlace:     "BKK",
						DepartDate:       *************, // 2024-12-10 06:25
						ArrivalDate:      *************, // 2024-12-10 08:50
						FlightNumber:     "VJ815",
						CarrierMarketing: "VJ",
						CarrierOperator:  "VJ",
						Aircraft:         "737",
						DepartTerminal:   "1",
						ArrivalTerminal:  "2",
					},
				},
			},
		},
		Metadata: []*domain.Metadata{
			{
				Key:   domain.MetaKeySolution,
				Value: routing,
			},
		},
	}

	// Tạo PNR với chỉ 1 hành khách adult
	pnr := &domain.PNR{
		SessionID: "test-session-oneway-67890",
		ListPax: []*domain.PaxInfo{
			{
				ID:        1,
				Type:      enum.PaxTypeAdult,
				Surname:   "TRAN",
				GivenName: "VAN C",
				Gender:    1,                                                       // Male
				DOB:       func() *int64 { t := int64(************); return &t }(), // 1988-01-01
				Passport: &domain.PaxPassport{
					Number:         "C98765432",
					IssuingCountry: "VN",
					ExpiryDate:     *************, // 2030-01-01
				},
			},
		},
		ContactInfo: &domain.Contact{
			Surname:   "TRAN",
			GivenName: "VAN C",
			Phone:     "***********",
			Email:     "<EMAIL>",
			Gender:    1, // Male
		},
	}

	tracingID := "integration-test-create-booking-oneway"

	// Gọi CreateBooking method
	result, err := adapter.CreateBooking(context.Background(), bookingDetails, pnr, tracingID)

	// Kiểm tra kết quả
	if err != nil {
		t.Logf("CreateBooking OneWay returned error: %v", err)
		// Trong integration test thực tế, có thể expect error do không có credentials thật
		return
	}

	if result != nil {
		t.Logf("CreateBooking OneWay successful:")
		t.Logf("  BookingRef: %s", result.BookingRef)
		t.Logf("  OrderNumRef: %s", result.OrderNumRef)
		t.Logf("  LastTicketingDate: %d", result.LastTicketingDate)
		t.Logf("  AirlineSystem: %s", result.AirlineSystem)

		if result.ExpectedPrice != nil {
			t.Logf("  ExpectedPrice: %.2f %s", result.ExpectedPrice.Amount, result.ExpectedPrice.Currency)

			// Validate expected price for one-way (should be 800 + 200 = 1000)
			expectedAmount := 1000.0
			if result.ExpectedPrice.Amount != expectedAmount {
				t.Errorf("Expected price amount %.2f, got %.2f", expectedAmount, result.ExpectedPrice.Amount)
			}
		}

		// Validate response fields
		if result.BookingRef == "" {
			t.Error("Expected BookingRef to be non-empty")
		}
		if result.OrderNumRef == "" {
			t.Error("Expected OrderNumRef to be non-empty")
		}
		if result.AirlineSystem != "TC" {
			t.Errorf("Expected AirlineSystem to be 'TC', got '%s'", result.AirlineSystem)
		}
		if result.LastTicketingDate <= 0 {
			t.Error("Expected LastTicketingDate to be greater than 0")
		}
	} else {
		t.Log("CreateBooking OneWay returned nil result")
	}
}

func TestTongchengAdapter_CreateBooking_ErrorHandling_Integration(t *testing.T) {
	cfg := &config.Schema{
		TongChengBaseURL:    "xxx",
		TongChengPartnerKey: "xxx",
		TongChengUsername:   "xxx",
		TongChengPartnerID:  "xxx",
		ProxyURL:            "xxx",
	}

	tongchengClient := client.NewTongChengClient(cfg, nil)
	adapter := &tongchengAdapter{
		cfg:    cfg,
		client: tongchengClient,
	}

	// Test case 1: Nil BookingDetails
	t.Run("NilBookingDetails", func(t *testing.T) {
		pnr := &domain.PNR{SessionID: "test"}
		result, err := adapter.CreateBooking(context.Background(), nil, pnr, "test-trace")

		if err == nil {
			t.Error("Expected error for nil BookingDetails")
		}
		if result != nil {
			t.Error("Expected nil result for nil BookingDetails")
		}
		t.Logf("Error (expected): %v", err)
	})

	// Test case 2: Nil PNR
	t.Run("NilPNR", func(t *testing.T) {
		bookingDetails := &domain.BookingDetails{
			OfficeID:    "TEST",
			BookingCode: "TEST",
			FlightType:  enum.FlightTypeInternational,
		}
		result, err := adapter.CreateBooking(context.Background(), bookingDetails, nil, "test-trace")

		if err == nil {
			t.Error("Expected error for nil PNR")
		}
		if result != nil {
			t.Error("Expected nil result for nil PNR")
		}
		t.Logf("Error (expected): %v", err)
	})

	// Test case 3: Empty SessionID
	t.Run("EmptySessionID", func(t *testing.T) {
		bookingDetails := &domain.BookingDetails{
			OfficeID:    "TEST",
			BookingCode: "TEST",
			FlightType:  enum.FlightTypeInternational,
			Itineraries: []*domain.FlightItinerary{
				{
					DepartPlace:  "SGN",
					ArrivalPlace: "BKK",
					CabinClass:   "Y",
					Segments: []*domain.ItinerarySegment{
						{
							DepartPlace:      "SGN",
							ArrivalPlace:     "BKK",
							DepartDate:       *************,
							ArrivalDate:      *************,
							FlightNumber:     "VJ815",
							CarrierMarketing: "VJ",
						},
					},
				},
			},
		}

		pnr := &domain.PNR{
			SessionID: "", // Empty session ID
			ListPax: []*domain.PaxInfo{
				{
					ID:        1,
					Type:      enum.PaxTypeAdult,
					Surname:   "TEST",
					GivenName: "USER",
					Gender:    1,
				},
			},
			ContactInfo: &domain.Contact{
				Surname:   "TEST",
				GivenName: "USER",
				Phone:     "123456789",
				Email:     "<EMAIL>",
				Gender:    1,
			},
		}

		result, err := adapter.CreateBooking(context.Background(), bookingDetails, pnr, "test-trace")

		// Có thể expect error hoặc không tùy thuộc vào implementation
		t.Logf("Result for empty SessionID - Error: %v, Result: %v", err, result)
	})

	// Test case 4: Missing Metadata
	t.Run("MissingMetadata", func(t *testing.T) {
		bookingDetails := &domain.BookingDetails{
			OfficeID:    "TEST",
			BookingCode: "TEST",
			FlightType:  enum.FlightTypeInternational,
			Itineraries: []*domain.FlightItinerary{
				{
					DepartPlace:  "SGN",
					ArrivalPlace: "BKK",
					CabinClass:   "Y",
					Segments: []*domain.ItinerarySegment{
						{
							DepartPlace:      "SGN",
							ArrivalPlace:     "BKK",
							DepartDate:       *************,
							ArrivalDate:      *************,
							FlightNumber:     "VJ815",
							CarrierMarketing: "VJ",
						},
					},
				},
			},
			// Không có Metadata - có thể gây lỗi
		}

		pnr := &domain.PNR{
			SessionID: "test-session-no-metadata",
			ListPax: []*domain.PaxInfo{
				{
					ID:        1,
					Type:      enum.PaxTypeAdult,
					Surname:   "TEST",
					GivenName: "USER",
					Gender:    1,
				},
			},
			ContactInfo: &domain.Contact{
				Surname:   "TEST",
				GivenName: "USER",
				Phone:     "123456789",
				Email:     "<EMAIL>",
				Gender:    1,
			},
		}

		result, err := adapter.CreateBooking(context.Background(), bookingDetails, pnr, "test-trace")

		// Log kết quả để xem behavior
		t.Logf("Result for missing metadata - Error: %v, Result: %v", err, result)
	})
}
