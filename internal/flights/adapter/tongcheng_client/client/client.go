package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"time"

	commonConstants "gitlab.deepgate.io/apps/common/constants"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/encryption"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	pkgConstants "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

const (
	methodPost = "POST"
)

type TongChengClient interface {
	SearchFlight(ctx context.Context, tracingID string, req *entities.SearchFlightRequest) (*entities.SearchFlightResponse, error)
	Verify(ctx context.Context, tracingID string, req *entities.VerifyRequest) (*entities.VerifyResponse, error)
	SearchRefund(ctx context.Context, tracingID string, req *entities.SearchRefundRequest) (*entities.SearchRefundResponse, error)
	Booking(ctx context.Context, tracingID string, req *entities.BookingRequest) (*entities.BookingResponse, error)
	OrderDetail(ctx context.Context, tracingID string, req *entities.OrderDetailRequest) (*entities.OrderDetailResponse, error)
	IssueTicket(ctx context.Context, tracingID string, req *entities.IssueTicketRequest) (*entities.IssueTicketResponse, error)
}

type tongchengClient struct {
	requestRepo repositories.RequestRepository
	encrypt     encryption.Encryption
	PID         string
	Username    string
	BaseURL     string
	Env         string
	ProxyURL    string
}

func NewTongChengClient(cfg *config.Schema, requestRepo repositories.RequestRepository) TongChengClient {
	return &tongchengClient{
		requestRepo: requestRepo,
		encrypt:     encryption.NewAESGzipEncryption(cfg.TongChengPartnerKey),
		BaseURL:     cfg.TongChengBaseURL,
		Username:    cfg.TongChengUsername,
		PID:         cfg.TongChengPartnerID,
		Env:         cfg.Env,
		ProxyURL:    cfg.ProxyURL,
	}
}

func (c *tongchengClient) getAuthInfo() entities.AuthInfo {
	return entities.AuthInfo{
		Username: c.Username,
	}
}

func (c *tongchengClient) getHeader() map[string]string {
	return map[string]string{
		"pid":          c.PID,
		"Content-Type": "text/plain; charset=utf-8",
	}
}

func (c *tongchengClient) prepareEncryptedBody(body interface{}) (string, error) {
	if body == nil {
		return "", nil
	}

	jsonBytes, err := json.Marshal(body)
	if err != nil {
		return "", fmt.Errorf("marshal error: %w", err)
	}

	return c.encrypt.EncryptAndCompress(string(jsonBytes))
}

func (c *tongchengClient) do(
	ctx context.Context,
	relativePath string,
	method string,
	body interface{},
	headers map[string]string,
) (_ []byte, _ int, duration int64, _ error) {
	beginAt := time.Now().UnixMilli()

	defer func() {
		duration = time.Now().UnixMilli() - beginAt
	}()

	var requestBody io.Reader = nil

	if body != nil {
		encoded, err := c.prepareEncryptedBody(body)
		if err != nil {
			log.Error("Failed to prepare body", log.Any("error", err))
			return nil, 0, duration, err
		}
		requestBody = bytes.NewReader([]byte(encoded))

	}

	log.Info("request body", log.Any("requestBody", requestBody))
	opt := []tracingHttp.Option{}

	if c.Env != commonConstants.ProductionEnvName {
		opt = append(opt, tracingHttp.WithProxyURL(c.ProxyURL))
	}

	response, err := tracingHttp.CustomRawRequest(ctx, c.BaseURL+relativePath, method, requestBody, headers, opt...)
	if err != nil {
		log.Error("Do request from TongCheng platform error",
			log.Any("error", err),
			log.String("relative path", relativePath),
			log.Any("req", body))
		return nil, 0, duration, err
	}

	defer response.Body.Close() // nolint: errcheck

	bytesBuffer := bytes.Buffer{}
	_, err = io.Copy(&bytesBuffer, response.Body)
	if err != nil {
		log.Error("Read response error ",
			log.Any("error", err),
			log.String("relative path", relativePath))
		return nil, response.StatusCode, duration, err
	}

	resBody := bytesBuffer.Bytes()

	if response.StatusCode != http.StatusOK {
		log.Error("Status code not successful",
			log.Int("Status Code", response.StatusCode))
		return resBody, response.StatusCode, duration, errors.New(response.Status)
	}

	decoded, err := c.encrypt.DecompressAndDecrypt(string(resBody))
	if err != nil {
		log.Error("Decrypt response failed", log.Any("error", err))
		return nil, response.StatusCode, duration, err
	}

	return []byte(decoded), response.StatusCode, duration, nil
}

func (c *tongchengClient) hideClientInfoRequest(body interface{}) {
	stype := reflect.ValueOf(body).Elem()
	field := stype.FieldByName("userName")
	if field.IsValid() {
		field.SetString("")
	}
}

func (c *tongchengClient) hideHeaderRequest(headers map[string]string) {
	if headers == nil {
		return
	}
	delete(headers, "pid")
}

func (c *tongchengClient) doRequest(
	ctx context.Context,
	relativePath string,
	method string,
	body interface{},
	tracingID string,
) ([]byte, error) {
	headers := c.getHeader()

	data, statusCode, duration, err := c.do(ctx, relativePath, method, body, headers)

	headerClone := map[string]string{}
	for key, val := range headers {
		headerClone[key] = val
	}

	go func(headerClone map[string]string) {
		fullPath := c.BaseURL + relativePath
		bCtx, cancel := context.WithTimeout(context.Background(), pkgConstants.RequestRepoCtxTimeout)
		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		c.hideClientInfoRequest(body)
		c.hideHeaderRequest(headerClone)

		req := &repositories.Request{
			Provider:   enum.FlightProviderTongCheng,
			RequestID:  requestID,
			Path:       fullPath,
			Method:     method,
			Body:       body,
			Headers:    headerClone,
			Response:   data,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     relativePath,
			IsJson:     true,
			ErrorMsg:   err,
			TracingID:  tracingID,
		}

		if err = c.requestRepo.Create(bCtx, req); err != nil {
			log.Error("doRequest requestRepo.Create error",
				log.Any("error", err),
				log.Any("req", req),
			)
		}
	}(headerClone)

	return data, err
}

func (c *tongchengClient) SearchFlight(ctx context.Context, tracingID string, req *entities.SearchFlightRequest) (*entities.SearchFlightResponse, error) {
	req.AuthInfo = c.getAuthInfo()

	resBody, err := c.doRequest(ctx, constants.APISearchFlight, methodPost, req, tracingID)
	if err != nil {
		return nil, err
	}

	var res *entities.SearchFlightResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}

	return res, nil
}

func (c *tongchengClient) Verify(ctx context.Context, tracingID string, req *entities.VerifyRequest) (*entities.VerifyResponse, error) {
	req.AuthInfo = c.getAuthInfo()

	resBody, err := c.doRequest(ctx, constants.APIVerify, methodPost, req, tracingID)
	if err != nil {
		return nil, err
	}

	var res *entities.VerifyResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}

	return res, nil
}

func (c *tongchengClient) SearchRefund(ctx context.Context, tracingID string, req *entities.SearchRefundRequest) (*entities.SearchRefundResponse, error) {
	req.AuthInfo = c.getAuthInfo()

	resBody, err := c.doRequest(ctx, constants.APISearchRefund, methodPost, req, tracingID)
	if err != nil {
		return nil, err
	}

	var res *entities.SearchRefundResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}

	return res, nil
}

func (c *tongchengClient) Booking(ctx context.Context, tracingID string, req *entities.BookingRequest) (*entities.BookingResponse, error) {
	req.AuthInfo = c.getAuthInfo()

	resBody, err := c.doRequest(ctx, constants.APIBooking, methodPost, req, tracingID)
	if err != nil {
		return nil, err
	}

	var res *entities.BookingResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}

	return res, nil
}

func (c *tongchengClient) OrderDetail(ctx context.Context, tracingID string, req *entities.OrderDetailRequest) (*entities.OrderDetailResponse, error) {
	req.AuthInfo = c.getAuthInfo()

	resBody, err := c.doRequest(ctx, constants.APIOrderDetail, methodPost, req, tracingID)
	if err != nil {
		return nil, err
	}

	var res *entities.OrderDetailResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}

	return res, nil
}

func (c *tongchengClient) IssueTicket(ctx context.Context, tracingID string, req *entities.IssueTicketRequest) (*entities.IssueTicketResponse, error) {
	resBody, err := c.doRequest(ctx, constants.APINotifications, methodPost, req, tracingID)
	if err != nil {
		return nil, err
	}

	var res *entities.IssueTicketResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}

	return res, nil
}

func (c *tongchengClient) CancelBooking(ctx context.Context, tracingID string, req *entities.CancelBookingRequest) (*entities.CancelBookingResponse, error) {
	resBody, err := c.doRequest(ctx, constants.APINotifications, methodPost, req, tracingID)
	if err != nil {
		return nil, err
	}

	var res *entities.CancelBookingResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}

	return res, nil
}
