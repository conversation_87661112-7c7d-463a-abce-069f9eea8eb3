package enum

import (
	"bytes"
	"fmt"
	"strings"
)

type PassengerType uint8

const (
	PassengerTypeNone PassengerType = iota
	PassengerTypeADT
	PassengerTypeCHD
)

var PassengerTypeName = map[PassengerType]string{
	PassengerTypeNone: "",
	PassengerTypeADT:  "ADT",
	PassengerTypeCHD:  "CHD",
}

var PassengerTypeValue = func() map[string]PassengerType {
	value := map[string]PassengerType{}
	for k, v := range PassengerTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e PassengerType) MarshalJSON() ([]byte, error) {
	v, ok := PassengerTypeName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *PassengerType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := PassengerTypeValue[strings.ToUpper(string(data))]
	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (*PassengerType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range PassengerTypeName {
		vals = append(vals, name)
	}

	return vals
}
