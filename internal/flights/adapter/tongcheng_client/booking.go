package tongcheng_client

import (
	"context"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func (a *tongchengAdapter) CreateBooking(ctx context.Context, bookingDetails *domain.BookingDetails, pnr *domain.PNR, tracingID string) (*domain.SvcCreateBookingResponse, error) {
	bookingReq := converts.ToTongChengBookingRequest(bookingDetails, pnr)
	res, err := a.client.Booking(ctx, tracingID, bookingReq)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	response, err := converts.FromTongChengBookingResponse(res, bookingReq.BookingParams.Routing)
	if err != nil {
		return nil, err
	}

	return response, nil
}
