package tongcheng_client

import (
	"context"
	"sync"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

type tongchengAdapter struct {
	cfg    *config.Schema
	client client.TongChengClient
}

type TongChengAdapter interface {
	SearchFlights(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) ([]*domain.ResponseFlight, error)
	CheckFare(ctx context.Context, req *domain.SearchFlightsRequest, flight *domain.ResponseFlight, tracingID string) (*domain.CheckFareInfo, error)
	CreateBooking(ctx context.Context, bookingDetails *domain.BookingDetails, tracingID string) (*domain.SvcCreateBookingResponse, error)
}

func NewTongChengAdapter(cfg *config.Schema, requestRepo repositories.RequestRepository) TongChengAdapter {
	{
		return &tongchengAdapter{
			cfg:    cfg,
			client: client.NewTongChengClient(cfg, requestRepo),
		}
	}
}

func (a *tongchengAdapter) SearchFlights(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) ([]*domain.ResponseFlight, error) {
	if req == nil || req.IsMultiItinerary() || req.Passengers.INF > 0 {
		return nil, nil
	}

	out := []*domain.ResponseFlight{}
	var (
		wg      sync.WaitGroup
		mu      sync.Mutex
		errChan = make(chan error, len(constants.CabinClasses))
	)

	for _, class := range constants.CabinClasses {
		wg.Add(1)
		go func() {
			defer wg.Done()

			clientReq := converts.ToSearchFlightRequest(req, class)

			data, err := a.client.SearchFlight(ctx, tracingID, clientReq)
			if err != nil {
				errChan <- errors.Wrap(err, "client.SearchFlight")
				return
			}

			res, err := converts.ToDomainResponseFlights(data, req)
			if err != nil {
				errChan <- errors.Wrap(err, "ToDomainResponseFlights")
				return
			}

			mu.Lock()
			out = append(out, res...)
			mu.Unlock()
		}()
	}

	wg.Wait()

	close(errChan)

	for err := range errChan {
		return nil, err
	}

	return out, nil
}
