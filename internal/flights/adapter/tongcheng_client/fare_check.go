package tongcheng_client

import (
	"context"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func (a *tongchengAdapter) CheckFare(ctx context.Context, req *domain.SearchFlightsRequest, flight *domain.ResponseFlight, tracingID string) (*domain.CheckFareInfo, error) {
	verifyReq, err := converts.ToTongChengVerifyRequest(req, flight)
	if err != nil {
		return nil, err
	}

	verifyRes, err := a.client.Verify(ctx, tracingID, verifyReq)
	if err != nil {
		return nil, err
	}

	if verifyRes == nil || verifyRes.Routing == nil || verifyRes.Routing.Data == "" {
		return nil, nil
	}

	if verifyRes.Status == constants.TongchengStatusA100 {
		return nil, domain.ErrItinerarySoldOut
	}

	refundReq, err := converts.ToTongChengSearchRefundRequest(verifyRes.Routing.Data, tracingID)
	if err != nil {
		return nil, err
	}

	searchRefundRes, err := a.client.SearchRefund(ctx, tracingID, refundReq)
	if err != nil {
		return nil, err
	}

	if searchRefundRes == nil || searchRefundRes.ChangeRules == nil || searchRefundRes.RefundRules == nil {
		return nil, nil
	}

	if verifyRes.Status == constants.TongchengStatusA100 {
		return nil, domain.ErrItinerarySoldOut
	}

	// TODO: convert to domain.
	return nil, nil
}
