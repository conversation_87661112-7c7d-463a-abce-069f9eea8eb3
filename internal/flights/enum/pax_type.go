package enum

type PaxType string

const (
	PaxTypeNone     PaxType = ""
	PaxTypeAdult    PaxType = "ADT"
	PaxTypeChildren PaxType = "CHD"
	PaxTypeInfant   PaxType = "INF"
)

const (
	PaxTypeNoneInt = iota
	PaxTypeAdultInt
	PaxTypeChildrenInt
	PaxTypeInfantInt
)

var PaxTypeMap = map[int]PaxType{
	PaxTypeNoneInt:     PaxTypeNone,
	PaxTypeAdultInt:    PaxTypeAdult,
	PaxTypeChildrenInt: PaxTypeChildren,
	PaxTypeInfantInt:   PaxTypeInfant,
}

var PaxReverseMap = map[PaxType]int{
	PaxTypeNone:     PaxTypeNoneInt,
	PaxTypeAdult:    PaxTypeAdultInt,
	PaxTypeChildren: PaxTypeChildrenInt,
	PaxTypeInfant:   PaxTypeInfantInt,
}
