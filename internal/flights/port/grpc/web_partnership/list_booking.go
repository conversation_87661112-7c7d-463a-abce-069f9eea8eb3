package webpartnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/port/grpc/web_partnership/converts"
)

// ListFlightBooking implements FlightBookingServiceServer.ListFlightBooking
// Danh sách booking chuyến bay với các filter và pagination
func (s *WebPartnershipServer) ListFlightBooking(ctx context.Context, req *web_partnership.ListFlightBookingReq) (*web_partnership.ListFlightBookingRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	if isContain := commonHelpers.Contains[string](user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleOfficeManager]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	// Convert proto request to filter query request
	queryReq, err := converts.ToListBookingFilterRequest(req)
	if err != nil {
		log.Error("Failed to convert proto request to query request",
			log.Any("error", err),
			log.Any("req", req))
		return &web_partnership.ListFlightBookingRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	queryReq.ManagerID = user.Id

	// Call application layer to get booking list using filter query
	queryRes, err := s.app.Queries.ListBookingFilterHandler.Handle(ctx, queryReq)
	if err != nil {
		log.Error("Failed to get booking list",
			log.Any("error", err),
			log.Any("queryReq", queryReq))
		return &web_partnership.ListFlightBookingRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	if queryRes == nil {
		return &web_partnership.ListFlightBookingRes{
			IsSuccess:  true,
			Items:      []*web_partnership.FlightBookingListItem{},
			Pagination: nil,
		}, nil
	}

	// Enrich bookings with passengers and agent code
	enrichedBookings, err := s.app.Services.BookingEnrichment.EnrichBookingListWithPaxAndAgent(ctx, queryRes.Bookings, user.PartnershipId)
	if err != nil {
		log.Error("Failed to enrich bookings",
			log.Any("error", err))
		// Continue with original bookings if enrichment fails
		protoItems := converts.ToFlightBookingListItems(queryRes.Bookings)

		// Create pagination response from query result
		pagination := commonDomain.ToPagingProto(queryRes.Pagination)
		return &web_partnership.ListFlightBookingRes{
			IsSuccess:  true,
			Items:      protoItems,
			Pagination: pagination,
		}, nil
	}

	// Convert enriched domain bookings to proto items
	protoItems := converts.ToFlightBookingListItemsFromEnriched(enrichedBookings)

	// Create pagination response from query result
	pagination := commonDomain.ToPagingProto(queryRes.Pagination)

	return &web_partnership.ListFlightBookingRes{
		IsSuccess:  true,
		Items:      protoItems,
		Pagination: pagination,
	}, nil
}
