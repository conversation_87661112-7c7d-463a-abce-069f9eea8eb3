// DO NOT EDIT: code generated from 'gen-tracing.go'
package tongcheng_client_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type tongChengAdapterTrace struct {
	tongcheng_client.TongChengAdapter
}

func NewTongChengAdapter(arg1 *config.Schema,
	arg2 repositories.RequestRepository,

) tongcheng_client.TongChengAdapter {
	return &tongChengAdapterTrace{
		TongChengAdapter: tongcheng_client.NewTongChengAdapter(
			arg1,
			arg2,
		),
	}
}

func (s *tongChengAdapterTrace) CheckFare(ctx context.Context, arg2 *domain.SearchFlightsRequest, arg3 *domain.ResponseFlight, arg4 string) (*domain.CheckFareInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tongChengAdapter.CheckFare")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.TongChengAdapter.CheckFare(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TongChengAdapter.CheckFare failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *tongChengAdapterTrace) SearchFlights(ctx context.Context, arg2 *domain.SearchFlightsRequest, arg3 string) ([]*domain.ResponseFlight, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "tongChengAdapter.SearchFlights")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.TongChengAdapter.SearchFlights(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TongChengAdapter.SearchFlights failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
