// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vietjet_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna1a_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type fareServiceTrace struct {
	service.FareService
}

func NewFareService(arg1 *config.Schema,
	arg2 vna_client.VNAAdapter,
	arg3 vietjet_client.VietjetAdapter,
	arg4 travel_fusion.TravelFusionAdapter,
	arg5 amadeus_client.AmadeusAdapter,
	arg6 ev_client.EVAdapter,
	arg7 ev_international_client.EVInternationalAdapter,
	arg8 repositories.AmadeusPNRRepository,
	arg9 repositories.BookingRepository,
	arg10 vna1a_client.VNA1AAdapter,
	arg11 webhook.WebhookAdapter,
	arg12 hnh_client.HNHAdapter,
	arg13 pkfare_client.PkfareAdapter,
	arg14 service.PriceService,
	arg15 tongcheng_client.TongChengAdapter,

) service.FareService {
	return &fareServiceTrace{
		FareService: service.NewFareService(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
			arg11,
			arg12,
			arg13,
			arg14,
			arg15,
		),
	}
}

func (s *fareServiceTrace) CheckFare(ctx context.Context, arg2 enum.FlightProvider, arg3 *domain.SearchFlightsRequest, arg4 *domain.ResponseFlight) (*domain.CheckFareInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "fareService.CheckFare")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.FareService.CheckFare(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.FareService.CheckFare failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *fareServiceTrace) ConfirmFare(ctx context.Context, arg2 *domain.BookingSession, arg3 *domain.PNR, arg4 map[enum.FlightProvider][]string, arg5 *domain.WebhookCfg) (*domain.TotalFareInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "fareService.ConfirmFare")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2 := s.FareService.ConfirmFare(ctx, arg2, arg3, arg4, arg5)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.FareService.ConfirmFare failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
