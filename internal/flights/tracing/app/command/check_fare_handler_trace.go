// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	redisrepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type checkFareHandlerTrace struct {
	command.CheckFareHandler
}

func NewCheckFareHandler(arg1 service.FareService,
	arg2 service.SessionService,
	arg3 service.SearchFlightsService,
	arg4 service.BookingService,
	arg5 service.SupplierRouteService,
	arg6 service.L2bService,
	arg7 redisrepo.SearchFlightsRepository,
	arg8 repositories.ListFlightRepository,
	arg9 service.ListFlightService,
	arg10 service.CurrencyExchangeService,
	arg11 service.PriceService,
	arg12 config.Schema,

) command.CheckFareHandler {
	return &checkFareHandlerTrace{
		CheckFareHandler: command.NewCheckFareHandler(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
			arg11,
			arg12,
		),
	}
}

func (s *checkFareHandlerTrace) Handle(ctx context.Context, arg2 *domain.CheckFareRequest) (*domain.CheckFareResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "checkFareHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.CheckFareHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CheckFareHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
