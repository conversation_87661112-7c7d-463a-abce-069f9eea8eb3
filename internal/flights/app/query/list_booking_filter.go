package query

import (
	"context"
	"fmt"

	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/internal_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

// ListBookingFilterHandler handles booking list queries with filters
type ListBookingFilterHandler interface {
	Handle(ctx context.Context, req *domain.ListBookingFilter) (*ListBookingFilterResponse, error)
}

type listBookingFilterHandler struct {
	bookingRepo    repositories.BookingRepository
	internalClient internal_client.InternalClient
}

// ListBookingFilterResponse represents the response with bookings and metadata
type ListBookingFilterResponse struct {
	Bookings   []*domain.BookingSession   `json:"bookings"`
	TotalCount int64                      `json:"total_count"`
	Pagination *commonDomain.Pagination   `json:"pagination"`
	Summary    *domain.BookingListSummary `json:"summary"`
	Filters    *domain.FilterInfo         `json:"applied_filters"`
}

// NewListBookingFilterHandler creates a new booking list handler with filters
func NewListBookingFilterHandler(bookingRepo repositories.BookingRepository, internalClient internal_client.InternalClient) ListBookingFilterHandler {
	return &listBookingFilterHandler{
		bookingRepo:    bookingRepo,
		internalClient: internalClient,
	}
}

// Handle processes the booking list request with filters
func (h *listBookingFilterHandler) Handle(ctx context.Context, req *domain.ListBookingFilter) (*ListBookingFilterResponse, error) {
	// Set default pagination if not provided
	if req.Pagination == nil {
		req.Pagination = &commonDomain.Pagination{
			PageCurrent: 1,
			PageLimit:   20,
		}
	}

	shops, err := h.internalClient.GetShopsByManagerID(ctx, req.PartnershipID, req.ManagerID)
	if err != nil {
		log.Error("internalClient.GetShopsByManagerID error", log.Any("error", err), log.Any("req", req))
		return nil, err
	}

	if len(shops) == 0 {
		return nil, nil
	}

	for _, shop := range shops {
		req.ManageOfficeIDs = append(req.ManageOfficeIDs, shop.OfficeID)
	}

	// Use repository method
	result, err := h.bookingRepo.ListBookingFilter(ctx, req)
	if err != nil {
		log.Error("Failed to get bookings with filters",
			log.Any("error", err),
			log.Any("req", req))
		return nil, fmt.Errorf("failed to get bookings: %w", err)
	}

	// Validate result
	if result == nil {
		log.Error("Repository returned nil result")
		return nil, fmt.Errorf("repository returned nil result")
	}

	// Convert domain result to query response with safe defaults
	bookings := result.Bookings
	if bookings == nil {
		bookings = []*domain.BookingSession{}
	}

	response := &ListBookingFilterResponse{
		Bookings:   bookings,
		Pagination: result.Pagination,
		Summary:    result.Summary,
		Filters:    result.FilterInfo,
	}

	return response, nil
}
