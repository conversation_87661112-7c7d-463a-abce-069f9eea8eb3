package app

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
)

type Application struct {
	Commands Commands
	Queries  Queries
	Services Services
}

type Commands struct {
	CheckFareV2Handler                      command.CheckFareV2Handler
	CheckFareHandler                        command.CheckFareHandler
	AddPNRHandler                           command.AddPNRHandler
	ModPNRHandler                           command.ModPNRHandler
	ConfirmFareHandler                      command.ConfirmFareHandler
	CreateBookingHandler                    command.CreateBookingHandler
	IssueTicketHandler                      command.IssueTicketHandler
	ProcessPendingTicket                    command.ProcessPendingTicketHandler
	ProcessSaveMultipleSupplierRoutesDaily  command.ProcessSaveMultipleSupplierRoutesDailyHandler
	ProcessExpiredBookingHandler            command.ProcessExpiredBookingHandler
	DeleteExpiredRoutingData                command.DeleteExpiredRoutingDataHandler
	BulkInsertL2bItem                       command.BulkInsertL2bItemHandler
	UpdateTicketAndReservationCode          command.UpdateTicketAndReservationCodeHandler
	ManualCancelBooking                     command.ManualCancelBookingHandler
	UpdateBookingTransfer                   command.UpdateBookingTransferHandler
	RetrievePNRAmadeusForTransferredBooking command.RetrievePNRAmadeusForTransferredBookingHandler

	// SSR Template
	CreateAmaSSRTemplate          command.CreateAmaSSRTemplateHandler
	UpdateAmaSSRTemplate          command.UpdateAmaSSRTemplateHandler
	SetBookingNotified            command.SetBookingNotifiedHandler
	PrepareBookingHandler         command.PrepareBookingHandler
	CreateCurrencyExchangeHandler command.CreateCurrencyExchangeHandler
	UpdateCurrencyExchangeHandler command.UpdateCurrencyExchangeHandler
}

type Queries struct {
	SearchFlightsHandler         query.SearchFlightsHandler
	RetrieveBookingHandler       query.RetrieveBookingHandler
	LoginHandler                 query.LoginHandler
	TransactionHistoryHandler    query.TransactionHistoryHandler
	ListBooking                  query.ListBookingHandler
	ListBookingByTicketStatuses  query.ListManualIssuingBookingsHandler
	ListExpiredBooking           query.ListExpiredBookingHandler
	GetSeatMap                   query.GetSeatMapHandler
	GetInMemL2b                  query.GetInMemL2bHandler
	GetL2b                       query.GetL2bHandler
	GetBalanceHandler            query.GetBalanceHandler
	GetBookingByCode             query.GetBookingByCodeHandler
	GetPRNBySession              query.GetPNRBySessionHandler
	GetBaggages                  query.GetBaggagesHandler
	GetReportBooking             query.GetReportBookingHandler
	ListUpcomingBookings         query.ListUpcomingBookingsHandler
	GetOfficeInfo                query.GetOfficeInfoHandler
	SearchFlightsV2Handler       query.SearchFlightsV2Handler
	ListCurrencyExchanggeHandler query.ListCurrencyExchangeHandler
	GetCurrencyExchangeHandler   query.GetCurrencyExchangeDetailHandler
	ListBookingFilterHandler     query.ListBookingFilterHandler
	GetBookingByID               query.GetBookingByIDHandler
}

type Services struct {
	BookingEnrichment service.BookingEnrichmentService
}
