package service

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelper "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/logger"
	"gitlab.deepgate.io/apps/common/tracing"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.opentelemetry.io/otel/codes"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	redisRepos "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	contextbinding "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/context_binding"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

type SearchFlightsService interface {
	Search(
		ctx context.Context,
		req *domain.SearchFlightsRequest,
		reqProviders []enum.FlightProvider,
		flightType enum.FlightType,
		cachedInfo domain.SFAdditionalCacheInfo,
		hashKey string,
		version enum.SearchVersion,
	) (*domain.SearchFlightsResBasicInfo,
		[]*domain.ResponseFlight,
		error,
	)
	SearchCached(
		ctx context.Context,
		req *domain.SearchFlightsRequest,
		reqProviders []enum.FlightProvider,
		hashKey string,
		version enum.SearchVersion,
	) (
		*domain.SearchFlightsResBasicInfo,
		[]*domain.ResponseFlight,
		[]enum.FlightProvider,
		domain.SFAdditionalCacheInfo,
		error,
	)
	RetrieveFlightsByIds(ctx context.Context, searchKey string, flightIds []string, hideBag bool) ([]*domain.ResponseFlight, *domain.SearchFlightsCachedRecord, error)
	RetrieveFlightByID(ctx context.Context, searchKey, flightID string) (*domain.ResponseFlight, *domain.SearchFlightsCachedRecord, error)
}

type searchFlightsService struct {
	cfg               *config.Schema
	searchFlightsRepo repositories.SearchFlightsRepository
	listFlightRepo    repositories.ListFlightRepository
	airportRepo       repositories.AirportRepository
	searchHandler     *ProviderSearchHandlerImps
	redisRepo         redisRepos.SearchFlightsRepository
	commSvc           CommissionService
	priceService      PriceService
}

func NewSearchFlightsService(
	cfg *config.Schema,
	searchFlightsRepo repositories.SearchFlightsRepository,
	listFlightRepo repositories.ListFlightRepository,
	airportRepo repositories.AirportRepository,
	providerSearchHandler *ProviderSearchHandlerImps,
	redisRepo redisRepos.SearchFlightsRepository,
	commSvc CommissionService,
	priceService PriceService,
) SearchFlightsService {
	if providerSearchHandler == nil {
		panic("nil providerSearchHandler")
	}

	return &searchFlightsService{cfg, searchFlightsRepo, listFlightRepo, airportRepo, providerSearchHandler, redisRepo, commSvc, priceService}
}

func validateSearchRequest(itineraries []*domain.ItineraryRequest, airportMap map[string]*domain.Airport) error {
	for _, iti := range itineraries {
		airport := airportMap[iti.DepartPlace]
		if airport == nil {
			return errors.WithMsg(errors.ErrInvalidInput, "Airport not found")
		}

		loc, err := time.LoadLocation(airport.Timezone)
		if err != nil {
			logger.Error("Load location error", logger.Any("err", err), logger.String("timezone", airport.Timezone))
			// skip validate
			return nil
		}

		now := time.Now().In(loc)
		departDate := time.UnixMilli(iti.DepartDate).UTC()
		if time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).Compare(time.Date(departDate.Year(), departDate.Month(), departDate.Day(), 0, 0, 0, 0, loc)) > 0 {
			return errors.WithMsg(errors.ErrInvalidInput, "Depart time in past")
		}
	}
	return nil
}

func (s *searchFlightsService) Search(
	ctx context.Context,
	req *domain.SearchFlightsRequest,
	reqProviders []enum.FlightProvider,
	flightType enum.FlightType,
	cachedInfo domain.SFAdditionalCacheInfo,
	hashKey string,
	version enum.SearchVersion,
) (_ *domain.SearchFlightsResBasicInfo,
	_ []*domain.ResponseFlight,
	err error,
) {
	var (
		wg    sync.WaitGroup
		mutex sync.Mutex
		lock  bool
	)

	defer func() {
		if err != nil && lock {
			s.redisRepo.ReleaseLock(hashKey)
		}
	}()

	data := map[enum.FlightProvider][]*domain.ResponseFlight{}

	provderSearchHandlersMap := s.getEnableProviders(reqProviders)

	configMap := make(map[enum.FlightProvider]*domain.ProviderConfig)
	for _, config := range req.ProvidersConfig {
		configMap[enum.FlightProvider(config.Provider)] = config
	}

	searchKey := cachedInfo.SearchKey
	if searchKey == "" {
		newID, err := uuid.NewUUID()
		if err != nil {
			log.Error("uuid.NewUUID error", log.Any("error", err))
			return nil, nil, errors.ErrSomethingOccurred
		}

		searchKey = newID.String()
	}

	// Prepare PP
	airportMap, err := s.getAirportMapFromIti(ctx, req.Itineraries)
	if err != nil {
		return nil, nil, err
	}

	err = validateSearchRequest(req.Itineraries, airportMap)
	if err != nil {
		return nil, nil, err
	}

	lock, err = s.redisRepo.AccquireLock(hashKey)
	if err != nil {
		log.Error("AccquireLock error", log.Any("error", err), log.String("hashKey", hashKey))
		return nil, nil, errors.ErrSomethingOccurred
	}

	if !lock {
		basic, flights, _, _, err := s.SearchCached(ctx, req, reqProviders, hashKey, version)
		if err != nil {
			log.Error("s.SearchCached error", log.Any("error", err), log.String("hashKey", hashKey), log.Any("providers", reqProviders), log.Any("req", req))
			return nil, nil, err
		}

		return basic, flights, nil
	}

	// New ctx
	dcps, _ := ctx.Value(contextbinding.ContextDCPsKey{}).(*domain.PartnerDCPs)
	newCtx := context.WithValue(context.Background(), contextbinding.ContextDCPsKey{}, dcps)
	backgroundSearchCtx, cc := context.WithTimeout(newCtx, time.Minute*5)
	defer cc()

	for provider, handler := range provderSearchHandlersMap {
		if provider == enum.FlightProviderNone {
			continue
		}

		provider := provider
		handler := handler
		wg.Add(1)
		go func() {
			defer wg.Done()

			ctxTimeout := time.Second * time.Duration(s.cfg.SearchFlightCtxTimeout)

			searchCtx, cc := context.WithTimeout(ctx, ctxTimeout)
			defer cc()

			searchCtx, span := tracing.StartSpanFromContext(searchCtx, fmt.Sprintf("searchFlightsService.Search.%s", enum.FlightProviderName[provider]))
			defer span.End()
			tracing.AddAttributes(span, searchCtx, req, searchKey)

			searchCtx = contextbinding.CopyDCPs(searchCtx, ctx)

			var providerRF []*domain.ResponseFlight
			var err error

			if version == enum.SearchVersionV2 {
				providerRF, err = handler.searchV2(searchCtx, req, searchKey)
			} else {
				providerRF, err = handler.search(searchCtx, req, searchKey)
			}

			if err != nil {
				err, ok := reflect.ValueOf(err).Interface().(error)
				if ok {
					span.SetStatus(codes.Error, fmt.Sprintf("s.SearchFlightsService.Search.%s failed", enum.FlightProviderName[provider]))
					span.RecordError(err)
				}

				log.Error("handler.search error", log.Any("error", err), log.Int("provider", int(provider)),
					log.Any("req", req), log.String("searchKey", searchKey))
				return
			}

			mutex.Lock()
			data[provider] = providerRF
			mutex.Unlock()
		}()
	}

	wg.Wait()

	// Post processing

	for provider, holders := range data {
		for _, flight := range holders {

			// Map data
			flight.VAT = true
			flight.TotalFareAmount = flight.BaseTotalFareAmount

			if provider == enum.FlightProviderTravelFusion {
				flight.VAT = false
			}

			//
			for idx, iti := range flight.Itineraries {
				iti.GenerateItiID()
				var err error

				s.ensureAirportMap(backgroundSearchCtx, airportMap, []string{iti.DepartPlace, iti.ArrivalPlace})
				if airportMap[iti.ArrivalPlace] == nil || airportMap[iti.DepartPlace] == nil {
					flight.IsDeleted = true
					break
				}

				iti.DepartDt, err = helpers.ParseFakeUTCToRealTimeWithTz(iti.DepartDate, airportMap[iti.DepartPlace].Timezone, nil)
				if err != nil {
					log.Error("helpers.ParseFakeUTCToRealTimeWithTz error", log.Any("error", err), log.Int64("departDate", iti.DepartDate), log.String("tzMapping", airportMap[iti.DepartPlace].Timezone))
					return nil, nil, errors.ErrSomethingOccurred
				}

				iti.ArrivalDt, err = helpers.ParseFakeUTCToRealTimeWithTz(iti.ArrivalDate, airportMap[iti.ArrivalPlace].Timezone, nil)
				if err != nil {
					log.Error("helpers.ParseFakeUTCToRealTimeWithTz error", log.Any("error", err), log.Int64("ArrivalDate", iti.ArrivalDate), log.String("tzMapping", airportMap[iti.ArrivalPlace].Timezone))
					return nil, nil, errors.ErrSomethingOccurred
				}

				if idx != 0 && flight.Itineraries[idx].DepartDt.Sub(flight.Itineraries[idx-1].ArrivalDt) < time.Hour*3 {
					flight.IsDeleted = true
					break
				}

				iti.DepartCountry = airportMap[iti.DepartPlace].Country
				iti.ArrivalCountry = airportMap[iti.ArrivalPlace].Country

				if iti.FlightDuration == 0 {
					iti.FlightDuration = int(iti.ArrivalDt.Sub(iti.DepartDt).Minutes())
				}
			}
		}
		data[provider] = lo.Filter(holders, s.filterListFlight(flightType, configMap))
	}

	// Response
	res := []*domain.ResponseFlight{}

	for _, holder := range data {
		for _, flight := range holder {
			if !flight.Dispose {
				for _, miniRule := range flight.MiniRules {
					for _, penaltyRule := range miniRule.PenaltyRules {
						if penaltyRule.Amount == nil {
							if penaltyRule.Percent != nil {
								var amount float64
								switch penaltyRule.BaseType {
								case enum.BaseTypeFareAmount:
									amount = *penaltyRule.Percent * flight.TotalFareAmount
								case enum.BaseTypeFareBasic:
									amount = *penaltyRule.Percent * flight.TotalFareBasic
								}
								penaltyRule.Amount = &amount
							}
						}
					}
				}
				res = append(res, flight)
			}
		}
	}

	// Temporary lock and cache search results
	lock, err = s.redisRepo.AccquireCachingLock(searchKey)
	if err != nil {
		log.Error("redisRepo.AccquireCachingLock error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, err
	}

	if !lock {
		log.Error("redisRepo.AccquireCachingLock acquire lock error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, err
	}

	go func(data map[enum.FlightProvider][]*domain.ResponseFlight) {
		defer s.redisRepo.ReleaseLock(hashKey)
		defer s.redisRepo.ReleaseCachingLock(searchKey)

		if len(data) == 0 {
			log.Error("Prepare cache search results empty data error")
			return
		}

		bgCtx, cancel := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
		defer cancel()

		s.cacheSearchResults(bgCtx, req, hashKey, searchKey, data, flightType, version)
	}(data)

	if s.priceService != nil && len(res) > 0 {
		err = s.priceService.CalculateSearchFlightsPricing(ctx, res, &req.Passengers, flightType, req.OfficeID, s.cfg.HubPartnershipID)
		if err != nil {
			log.Error("s.priceService.CalculateSearchFlightsPricing error", log.Any("error", err), log.Any("req", req))
		}

		err = s.priceService.CalculatePenaltySearchFlight(ctx, s.cfg.HubPartnershipID, req.OfficeID, res)
		if err != nil {
			log.Error("s.priceService.CalculateSearchFlightsPricing error", log.Any("error", err), log.Any("req", req))
		}
	}

	return &domain.SearchFlightsResBasicInfo{
		Key:        searchKey,
		FlightType: flightType,
	}, res, nil
}

func (s *searchFlightsService) ensureAirportMap(ctx context.Context, airportMap map[string]*domain.Airport, codes []string) {
	requestCodes := []string{}

	for _, code := range codes {
		if airportMap[code] == nil {
			requestCodes = append(requestCodes, code)
		}
	}

	if len(requestCodes) != 0 {
		newAirportMap, err := s.airportRepo.FindByCodes(ctx, requestCodes)
		if err != nil {
			log.Error("s.airportRepo.FindTzByCodes error", log.Any("error", err), log.Any("requestCodes", requestCodes))
			return
		}

		for key, val := range newAirportMap {
			airportMap[key] = val
		}
	}
}

func (s *searchFlightsService) getAirportMapFromIti(ctx context.Context, ities []*domain.ItineraryRequest) (map[string]*domain.Airport, error) {
	airPortCodes := []string{}

	for _, item := range ities {
		airPortCodes = append(airPortCodes, item.DepartPlace, item.ArrivalPlace)
	}

	airPortCodes = lo.Uniq[string](airPortCodes)

	airportMap, err := s.airportRepo.FindByCodes(ctx, airPortCodes)
	if err != nil {
		log.Error("s.airportRepo.FindTzByCodes error", log.Any("error", err), log.Any("airportcodes", airPortCodes))
		return nil, errors.ErrSomethingOccurred
	}

	return airportMap, nil
}

func (s *searchFlightsService) cacheSearchResults(ctx context.Context, req *domain.SearchFlightsRequest, hashKey string, searchKey string, data map[enum.FlightProvider][]*domain.ResponseFlight, flightType enum.FlightType, version enum.SearchVersion) {
	defer s.redisRepo.ReleaseLock(hashKey)

	cacheDuration := constants.SearchFlightsCacheTime

	if constants.SearchFlightsShortCacheTime != constants.SearchFlightsCacheTime {
		isHashFlight, err := s.isHashFlight(ctx, req)
		if err != nil {
			return
		}

		if isHashFlight {
			cacheDuration = constants.SearchFlightsShortCacheTime
		}
	}

	expiredAt := time.Now().Add(cacheDuration).UnixMilli()

	reqs := make([]*domain.SearchFlightsCachedRecord, 0, len(data)+1)

	reqs = append(reqs, &domain.SearchFlightsCachedRecord{
		ID:         primitive.NewObjectID().Hex(),
		Index:      0,
		Key:        searchKey,
		HashKey:    hashKey,
		Provider:   enum.FlightProviderNone,
		ExpiredAt:  expiredAt,
		Request:    req,
		FlightType: flightType,
		Version:    version,
	})

	listFlight := []*domain.ResponseFlight{}

	for key, val := range data {
		recordID := primitive.NewObjectID().Hex()

		reqs = append(reqs, &domain.SearchFlightsCachedRecord{
			ID:         recordID,
			Index:      int(key),
			Key:        searchKey,
			HashKey:    hashKey,
			Provider:   key,
			ExpiredAt:  expiredAt,
			Request:    req,
			FlightType: flightType,
			Version:    version,
		})

		for _, flight := range val {
			flight.RecordID = recordID
			flight.ExpiredAt = expiredAt
			listFlight = append(listFlight, flight)
		}
	}

	if err := s.searchFlightsRepo.InsertMany(ctx, reqs); err != nil {
		log.Error("searchFlightsRepo.InsertMany error", log.Any("error", err))
		return
	}

	if err := s.listFlightRepo.InsertMany(ctx, listFlight); err != nil {
		log.Error("listFlightRepo.InsertMany error", log.Any("error", err))
		return
	}
}

func (s *searchFlightsService) filterListFlight(flightType enum.FlightType, configMap map[enum.FlightProvider]*domain.ProviderConfig) func(item *domain.ResponseFlight, index int) bool {
	return func(item *domain.ResponseFlight, index int) bool {
		if len(item.Itineraries) == 0 || item.IsDeleted {
			return false
		}

		if flightType == enum.FlightTypeInternational {
			config, exists := configMap[item.Provider]
			if !exists || !config.Enabled {
				return false
			}

			tripType := s.determineTripType(item)
			if tripType == enum.TripTypeNone {
				return false
			}

			itinerary := item.Itineraries[0]

			for _, fc := range config.FilterConfigs {
				if !fc.IsEnable || fc.TripType != tripType {
					continue
				}

				switch fc.FilterMode {
				case enum.FilterModeInclude:
					if len(fc.Airlines) > 0 && !s.isAllowedAirline(itinerary, fc.Airlines) {
						return false
					}
				case enum.FilterModeExclude:
					if len(fc.Airlines) == 0 || s.isAllowedAirline(itinerary, fc.Airlines) {
						return false
					}
				default:
					log.Warn("Unhandled FilterMode, skipping filter", log.Any("filterMode", fc.FilterMode))
				}
			}
		}

		// Filter loai bo chuyen bay noi dia qua canh o nuoc ngoai
		if flightType == enum.FlightTypeDomestic {
			var listAirport []string
			for _, iti := range item.Itineraries {
				for _, seg := range iti.Segments {
					listAirport = append(listAirport, seg.ArrivalPlace, seg.DepartPlace)
				}
			}

			listAirport = lo.Uniq[string](listAirport)

			for _, v := range listAirport {
				if !commonHelper.Contains[string](constants.AirportCodes, v) {
					return false
				}
			}
		}

		// Gio khoi hanh cach hien tai hon 3 tieng
		filterDepartFlightTime := s.filterDepartFlightTime(item.Itineraries[0].DepartDt)

		return filterDepartFlightTime
	}
}

func (s *searchFlightsService) filterDepartFlightTime(t time.Time) bool {
	return time.Until(t).Hours() > 3
}

func (s *searchFlightsService) getEnableProviders(req []enum.FlightProvider) map[enum.FlightProvider]ProviderSearchHandler {
	provderSearchHandlersMap := map[enum.FlightProvider]ProviderSearchHandler{}

	for _, p := range req {
		provderSearchHandlersMap[p] = s.searchHandler.getHandler(p)
	}

	return provderSearchHandlersMap
}

func (s *searchFlightsService) isHashFlight(ctx context.Context, req *domain.SearchFlightsRequest) (bool, error) {
	if len(req.Itineraries) == 0 {
		return false, errors.ErrInvalidInput
	}

	if req.Itineraries[0].DepartDate > time.Now().AddDate(0, 0, 2).UnixMilli() {
		return false, nil
	}

	ities := req.Itineraries

	airport, err := s.airportRepo.FindAirPort(ctx, ities[0].DepartPlace)
	if err != nil {
		log.Error("s.airportRepo.FindAirPort error", log.Any("error", err), log.String("code", ities[0].DepartPlace))
		return false, errors.ErrSomethingOccurred
	}

	loc, err := time.LoadLocation(airport.Timezone)
	if err != nil {
		log.Error("time.LoadLocation error", log.Any("error", err), log.String("timezone str", airport.Timezone))
		return false, errors.ErrSomethingOccurred
	}

	departDate := time.UnixMilli(ities[0].DepartDate).In(loc).UTC()
	now := time.Now().UTC()

	endOfDay := time.Date(departDate.Year(), departDate.Month(), departDate.Day(), 23, 59, 59, 0, loc)
	timeRemain := endOfDay.Sub(now)

	if timeRemain < time.Duration(0) {
		log.Error("negative time remain error", log.Int64("timeRemain", int64(timeRemain)))
		return false, errors.ErrInvalidInput
	}

	return timeRemain < constants.ShortCacheTimeThreshold, nil
}

func (s *searchFlightsService) SearchCached(
	ctx context.Context,
	req *domain.SearchFlightsRequest,
	reqProviders []enum.FlightProvider,
	hashKey string,
	version enum.SearchVersion,
) (
	*domain.SearchFlightsResBasicInfo,
	[]*domain.ResponseFlight,
	[]enum.FlightProvider,
	domain.SFAdditionalCacheInfo,
	error,
) {
	var cachedInfo domain.SFAdditionalCacheInfo

	returnProviders := []enum.FlightProvider{}
	flightType := enum.FlightTypeNone
	searchKey := ""

	err := s.redisRepo.WaitForLockToRelease(hashKey, time.Second, 70)
	if err != nil {
		log.Error("WaitForLockToRelease error", log.Any("error", err), log.String("hashKey", ""))
		return nil, nil, nil, cachedInfo, errors.ErrSomethingOccurred
	}

	cachedFlights, err := s.searchFlightsRepo.FindByHashKey(ctx, hashKey, reqProviders, version, time.Now().UnixMilli())
	if err != nil {
		log.Error("searchFlightsRepo.Find error", log.Any("error", err), log.String("hashKey", hashKey))
		return nil, nil, nil, cachedInfo, errors.ErrSomethingOccurred
	}

	recordIds := []string{}
	recordIDProviderMap := map[string]enum.FlightProvider{}

	for i, cFlights := range cachedFlights {
		if i == 0 {
			cachedInfo = domain.SFAdditionalCacheInfo{
				SearchKey: cFlights.Key,
			}
			flightType = cFlights.FlightType
			searchKey = cFlights.Key
		}

		recordIds = append(recordIds, cFlights.ID)
		recordIDProviderMap[cFlights.ID] = cFlights.Provider

		returnProviders = append(returnProviders, cFlights.Provider)
	}

	flights, err := s.listFlightRepo.FindRecIDs(ctx, recordIds)
	if err != nil {
		log.Error("listFlightRepo.FindRecIDs error", log.Any("error", err), log.Any("recordIDs", recordIds))
		return nil, nil, nil, cachedInfo, errors.ErrSomethingOccurred
	}

	flights = lo.Filter(flights, func(flight *domain.ResponseFlight, _ int) bool {
		return !flight.SoldOut
	})

	for _, flight := range flights {
		if flight.SoldOut {
			continue
		}
		flight.Provider = recordIDProviderMap[flight.RecordID]
	}

	if s.priceService != nil && len(flights) > 0 {
		err = s.priceService.CalculateSearchFlightsPricing(ctx, flights, &req.Passengers, flightType, req.OfficeID, s.cfg.HubPartnershipID)
		if err != nil {
			log.Error("s.priceService.CalculateSearchFlightsPricing error", log.Any("error", err), log.Any("req", req))
		}

		err = s.priceService.CalculatePenaltySearchFlight(ctx, s.cfg.HubPartnershipID, req.OfficeID, flights)
		if err != nil {
			log.Error("s.priceService.CalculateSearchFlightsPricing error", log.Any("error", err), log.Any("req", req))
		}
	}

	return &domain.SearchFlightsResBasicInfo{
		FlightType: flightType,
		Key:        searchKey,
	}, flights, returnProviders, cachedInfo, nil
}

func (s *searchFlightsService) RetrieveFlightByID(ctx context.Context, searchKey, flightID string) (*domain.ResponseFlight, *domain.SearchFlightsCachedRecord, error) {
	provider, err := utils.GetFlightProvider(flightID)
	if err != nil {
		log.Error("GetFlightProvider error", log.Any("error", err), log.String("flightID", flightID))
		return nil, nil, err
	}

	if provider == enum.FlightProviderNone {
		log.Error("Invalid provider error", log.Int64("provider", int64(provider)), log.String("flightID", flightID))
		return nil, nil, errors.ErrInvalidInput
	}

	searchRecords, err := s.searchFlightsRepo.FindByKey(ctx, searchKey, provider)
	if err != nil {
		log.Error("searchFlightsRepo.Find error", log.Any("error", err), log.String("key", searchKey))
		return nil, nil, errors.ErrSomethingOccurred
	}

	if len(searchRecords) == 0 {
		return nil, nil, errors.WithMsg(errors.ErrNotFound, constants.ErrMsgSearchKeyNotFound)
	}

	flight, err := s.listFlightRepo.FindByFlightID(ctx, flightID)
	if err != nil {
		log.Error("searchFlightsRepo.Find error", log.Any("error", err), log.String("flightID", flightID))
		return nil, nil, errors.ErrSomethingOccurred
	}

	flight.Provider = provider

	if flight == nil {
		return nil, nil, errors.WithMsg(errors.ErrNotFound, constants.ErrMsgFlightIDNotFound)
	}

	if flight.SoldOut {
		return nil, nil, domain.ErrItinerarySoldOut
	}

	for _, rec := range searchRecords {
		if flight.RecordID == rec.ID {
			return flight, rec, nil
		}
	}

	return nil, nil, errors.WithMsg(errors.ErrNotFound, constants.ErrMsgFlightIDNotFound)
}

// RetrieveFlightByIds retrieves flights from ids, must have SAME provider
func (s *searchFlightsService) RetrieveFlightsByIds(ctx context.Context, searchKey string, flightIds []string, hideBag bool) ([]*domain.ResponseFlight, *domain.SearchFlightsCachedRecord, error) {
	if len(flightIds) == 0 {
		return nil, nil, errors.ErrInvalidInput
	}

	// Must have same provider
	baseProvider := enum.FlightProviderNone

	for _, flightId := range flightIds {
		if flightId == "" {
			return nil, nil, errors.ErrInvalidInput
		}

		provider, err := utils.GetFlightProvider(flightId)
		if err != nil {
			log.Error("GetFlightProvider error", log.Any("error", err), log.Any("flightId", flightId))
			return nil, nil, domain.ErrInvalidProvider
		}

		if baseProvider == enum.FlightProviderNone {
			baseProvider = provider
		} else if baseProvider != provider {
			return nil, nil, domain.ErrInvalidFlightGroup
		}
	}

	flights, err := s.listFlightRepo.FindByFlightIds(ctx, flightIds, hideBag)
	if err != nil {
		log.Error("searchFlightsRepo.FindByFlightIds error", log.Any("error", err), log.Any("flightIds", flightIds))
		return nil, nil, errors.ErrSomethingOccurred
	}

	if len(flights) == 0 {
		return nil, nil, errors.WithMsg(errors.ErrNotFound, constants.ErrMsgFlightIDNotFound)
	}

	for _, flight := range flights {
		if flight.SoldOut {
			return nil, nil, domain.ErrItinerarySoldOut
		}
	}
	provider := baseProvider

	if provider == enum.FlightProviderNone {
		log.Error("Invalid provider error", log.Int64("provider", int64(provider)), log.Any("flightIDs", flightIds))
		return nil, nil, errors.ErrInvalidInput
	}

	searchRecords, err := s.searchFlightsRepo.FindByKey(ctx, searchKey, provider)
	if err != nil {
		log.Error("searchFlightsRepo.Find error", log.Any("error", err), log.String("key", searchKey))
		return nil, nil, errors.ErrSomethingOccurred
	}

	if len(searchRecords) == 0 {
		return nil, nil, errors.WithMsg(errors.ErrNotFound, constants.ErrMsgSearchKeyNotFound)
	}

	// Must same key
	var outFlights []*domain.ResponseFlight
	var outRec *domain.SearchFlightsCachedRecord

	seenID := map[string]bool{}
	for _, rec := range searchRecords {
		seenID[rec.ID] = true
		if flights != nil && flights[0] != nil && flights[0].RecordID == rec.ID {
			outFlights = flights
			outRec = rec
		}
	}

	if outFlights == nil || outRec == nil {
		return nil, nil, errors.WithMsg(errors.ErrNotFound, constants.ErrMsgFlightIDNotFound)
	}

	for _, flight := range outFlights {
		if !seenID[flight.RecordID] {
			return nil, nil, errors.ErrInvalidInput
		}
	}

	return outFlights, outRec, nil
}

func (s *searchFlightsService) determineTripType(item *domain.ResponseFlight) enum.TripType {
	if item == nil || len(item.Itineraries) == 0 {
		return enum.TripTypeNone
	}

	switch len(item.Itineraries) {
	case 1:
		it := item.Itineraries[0]
		switch {
		case commonHelper.Contains(constants.AirportCodes, it.DepartPlace):
			return enum.TripTypeSITI
		case commonHelper.Contains(constants.AirportCodes, it.ArrivalPlace):
			return enum.TripTypeSOTO
		default:
			return enum.TripTypeGLOBAL
		}
	case 2:
		it1, it2 := item.Itineraries[0], item.Itineraries[1]

		isRoundTrip := it1.ArrivalPlace == it2.DepartPlace &&
			it1.DepartPlace == it2.ArrivalPlace

		if isRoundTrip {
			switch {
			case commonHelper.Contains(constants.AirportCodes, it1.DepartPlace) && commonHelper.Contains(constants.AirportCodes, it2.ArrivalPlace):
				return enum.TripTypeSITI
			case !commonHelper.Contains(constants.AirportCodes, it1.DepartPlace) && commonHelper.Contains(constants.AirportCodes, it1.ArrivalPlace):
				return enum.TripTypeSOTO
			default:
				return enum.TripTypeGLOBAL
			}
		}

		return enum.TripTypeGLOBAL
	default:
		return enum.TripTypeGLOBAL
	}
}

func (s *searchFlightsService) isAllowedAirline(iti *domain.FlightItinerary, airlines []string) bool {
	for _, airline := range airlines {
		if iti.CarrierMarketing == airline {
			return true
		}
	}

	return false
}
