package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client"
	hnh_client "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vietjet_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna1a_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

type FareService interface {
	CheckFare(ctx context.Context, provider enum.FlightProvider, req *domain.SearchFlightsRequest, flight *domain.ResponseFlight) (*domain.CheckFareInfo, error)
	ConfirmFare(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR, suppliers map[enum.FlightProvider][]string, webhookCfg *domain.WebhookCfg) (*domain.TotalFareInfo, error)
}

type fareService struct {
	cfg                    *config.Schema
	vnaAdapter             vna_client.VNAAdapter
	vjAdapter              vietjet_client.VietjetAdapter
	travelFusionAdapter    travel_fusion.TravelFusionAdapter
	amadesusAdapter        amadeus_client.AmadeusAdapter
	evAdapter              ev_client.EVAdapter
	evInternationalAdapter ev_international_client.EVInternationalAdapter
	amadeusPNRRepo         repositories.AmadeusPNRRepository
	bookingRepo            repositories.BookingRepository
	vna1aAdapter           vna1a_client.VNA1AAdapter
	webhookClient          webhook.WebhookAdapter
	hnhAdapter             hnh_client.HNHAdapter
	pkfareAdapter          pkfare_client.PkfareAdapter
	priceService           PriceService
	tongChengAdapter       tongcheng_client.TongChengAdapter
}

func NewFareService(
	cfg *config.Schema,
	vnaAdapter vna_client.VNAAdapter,
	vjAdapter vietjet_client.VietjetAdapter,
	travelFusionAdapter travel_fusion.TravelFusionAdapter,
	amadesusAdapter amadeus_client.AmadeusAdapter,
	evAdapter ev_client.EVAdapter,
	evInternationalAdapter ev_international_client.EVInternationalAdapter,
	amadeusPNRRepo repositories.AmadeusPNRRepository,
	bookingRepo repositories.BookingRepository,
	vna1aAdapter vna1a_client.VNA1AAdapter,
	webhookClient webhook.WebhookAdapter,
	hnhAdapter hnh_client.HNHAdapter,
	pkfareAdapter pkfare_client.PkfareAdapter,
	priceService PriceService,
	tongChengAdapter tongcheng_client.TongChengAdapter,
) FareService {
	return &fareService{
		cfg:                    cfg,
		vnaAdapter:             vnaAdapter,
		vjAdapter:              vjAdapter,
		travelFusionAdapter:    travelFusionAdapter,
		amadesusAdapter:        amadesusAdapter,
		evAdapter:              evAdapter,
		evInternationalAdapter: evInternationalAdapter,
		amadeusPNRRepo:         amadeusPNRRepo,
		bookingRepo:            bookingRepo,
		vna1aAdapter:           vna1aAdapter,
		webhookClient:          webhookClient,
		hnhAdapter:             hnhAdapter,
		pkfareAdapter:          pkfareAdapter,
		priceService:           priceService,
		tongChengAdapter:       tongChengAdapter,
	}
}

func (f *fareService) vnaCheckFare(ctx context.Context, req *domain.SearchFlightsRequest, ities []*domain.FlightItinerary, tracingID string) error {
	segments := []*domain.ItinerarySegment{}

	for _, itinerary := range ities {
		segments = append(segments, itinerary.Segments...)
	}

	vnaRS, err := f.vnaAdapter.FareCheck(ctx, &req.Passengers, segments, tracingID)
	if err != nil {
		log.Error("f.vnaAdapter.FareCheck error", log.Any("error", err), log.Any("req.Passengers", req.Passengers), log.Any("segments", segments))
		return err
	}

	err = converts.MapBaggageToItinerary(ities, vnaRS.FreeBaggage)
	if err != nil {
		log.Error("converts.MapBaggageToItinerary error", log.Any("ities", ities), log.Any("vnaRS.FreeBag", vnaRS.FreeBaggage))
		return err
	}

	err = converts.UpdateFareBasis(ities, vnaRS.FareBasisData)
	if err != nil {
		log.Error("converts.UpdateFareBasis error", log.Any("ities", ities), log.Any("vnaRS.FareBasisData", vnaRS.FareBasisData))
		return err
	}

	return nil
}

func (f *fareService) CheckFare(ctx context.Context, provider enum.FlightProvider, req *domain.SearchFlightsRequest, flight *domain.ResponseFlight) (*domain.CheckFareInfo, error) {
	var (
		err error
	)

	if flight == nil {
		return nil, commonErrs.ErrInvalidInput
	}
	out := &domain.CheckFareInfo{}

	if provider.IsVJProvider() {
		err := f.vjAdapter.FareCheck(ctx, req, flight.Itineraries, provider, flight.FlightID, req.OfficeID)
		if err != nil {
			log.Error("f.vjFareCheck error", log.Any("error", err), log.Any("req", req), log.Any("flight", flight))
			return nil, err
		}
	}
	// VNA
	if provider == enum.FlightProviderVNA {
		err = f.vnaCheckFare(ctx, req, flight.Itineraries, flight.FlightID)
		if err != nil {
			log.Error("f.vnaCheckFare error", log.Any("error", err), log.Any("req", req), log.Any("flight", flight))
			return nil, err
		}
	}

	if provider == enum.FlightProviderPkfare {
		res, err := f.checkFarePkFare(ctx, req, flight.Itineraries, flight.FlightID)
		if err != nil {
			// If fare basic changed
			if errors.Is(err, domain.ErrFareBasisChanged) {
				res.ShouldUpdateFlight = true
				return res, err
			} else {
				log.Error("f.checkFarePkFare error", log.Any("error", err), log.Any("req", req), log.Any("flight", flight))
				return nil, err
			}

		}

		out.ShouldUpdateFlight = true
		out.MiniRules = res.MiniRules
	}

	if provider == enum.FlightProviderTongCheng {
		res, err := f.checkFareTongCheng(ctx, req, flight)
		if err != nil {
			if errors.Is(err, domain.ErrItinerarySoldOut) {
				return nil, err
			}
			log.Error("f.checkFareTongCheng error", log.Any("error", err), log.Any("req", req), log.Any("flight", flight))
			return nil, err
		}
		out.ShouldUpdateFlight = true
		out.MiniRules = res.MiniRules
	}

	// Apply price service calculation before returning
	if out.OriginTotalFareInfo != nil {
		log.Info("Applying price service calculation for CheckFare",
			log.Any("provider", provider),
			log.String("flight_id", flight.FlightID))

		updatedFareInfo, err := f.priceService.CalculateFarePricing(ctx, out.OriginTotalFareInfo, req, flight.Itineraries, flight.FlightID, provider)
		if err != nil {
			log.Error("Failed to calculate fare pricing in CheckFare",
				log.Any("error", err),
				log.Any("provider", provider),
				log.String("flight_id", flight.FlightID))
			return nil, err
		}
		out.OriginTotalFareInfo = updatedFareInfo
	}

	return out, nil
}

func (f *fareService) checkFarePkFare(ctx context.Context, req *domain.SearchFlightsRequest, ities []*domain.FlightItinerary, flightID string) (*domain.CheckFareInfo, error) {
	return f.pkfareAdapter.CheckFare(ctx, req, ities, flightID)
}

// checkFareTongCheng is a wrapper for tongChengAdapter.CheckFare.
// It calls CheckFare and returns the result directly.
// NOTE: Ensure that flight != nil
func (f *fareService) checkFareTongCheng(ctx context.Context, req *domain.SearchFlightsRequest, flight *domain.ResponseFlight) (*domain.CheckFareInfo, error) {
	if flight == nil {
		return nil, errors.New("flight is nil")
	}
	return f.tongChengAdapter.CheckFare(ctx, req, flight, flight.FlightID)
}

func (f *fareService) saveBookingRef(ctx context.Context, req *domain.AmadeusPNR) error {
	err := f.amadeusPNRRepo.InsertOne(ctx, req)
	if err != nil {
		log.Error("amadeusPNRRepo.InsertOne error", log.Any("error", err), log.Any("insertReq", req))
		return commonErrs.ErrSomethingOccurred
	}

	return nil
}

func (f *fareService) updateLastTKTDate(ctx context.Context, req *domain.AmadeusPNR) error {
	err := f.amadeusPNRRepo.Update(ctx, req.SessionID, req)
	if err != nil {
		log.Error("amadeusPNRRepo.Update error", log.Any("error", err), log.Any("UpdateReq", req))
		return commonErrs.ErrSomethingOccurred
	}
	//Update for case booking was created
	err = f.bookingRepo.UpdateBooking(ctx, req.SessionID, &domain.UpdateBookingRepoRequest{
		LastTicketingDate: req.LastTktDate,
		FareExpiredDate:   &req.FareExpiredDate,
		TicketExpiredDate: &req.TicketExpiredDate,
	})
	if err != nil {
		log.Error("bookingRepo.UpdateBooking error", log.Any("error", err), log.Any("UpdateReq", req))
		return commonErrs.ErrSomethingOccurred
	}
	return nil
}

func (f *fareService) internalConfirmFare(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR, _ map[enum.FlightProvider][]string, _ *domain.WebhookCfg) (*domain.TotalFareInfo, error) {
	if booking.Provider == enum.FlightProviderAmadeus {
		_, err := f.amadesusAdapter.ConfirmFareV2(ctx, booking, pnr, booking.SessionID)
		if err != nil {
			log.Error("amadesusAdapter.ConfirmFareV2 error", log.Any("error", err), log.Any("booking", booking), log.Any("pnr", pnr))
			return nil, err
		}
	}

	return booking.FareData, nil
}

func (f *fareService) ConfirmFare(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR, suppliers map[enum.FlightProvider][]string, webhookCfg *domain.WebhookCfg) (*domain.TotalFareInfo, error) {
	if booking.InternalBooking {
		return f.internalConfirmFare(ctx, booking, pnr, suppliers, webhookCfg)
	}

	// Amadeus confirm
	if booking.Provider == enum.FlightProviderAmadeus {
		res, err := f.amadesusAdapter.ConfirmFare(ctx, booking, pnr, false, booking.SessionID)
		if err != nil {
			log.Error("amadesusAdapter.ConfirmFare error", log.Any("error", err), log.Any("booking", booking), log.Any("pnr", pnr))
			return nil, err
		}

		if res.FareCf == nil {
			log.Error("amadesusAdapter.ConfirmFare totalFare nil error", log.Any("booking", booking), log.Any("pnr", pnr))
			return nil, commonErrs.ErrSomethingOccurred
		}

		if res.EmptySSRData {
			f.fetchAmaSSRBackgroundJob(booking, res, webhookCfg)
		}

		err = f.saveBookingRef(ctx, &domain.AmadeusPNR{
			SessionID:              booking.SessionID,
			RecordLocator:          res.RecordLocator,
			LastTktDate:            res.LastTktDate,
			SkipDefaultLastTktDate: res.SkipDefaultLastTktDate,
			FareExpiredDate:        res.FareExpiredDate,
			TicketExpiredDate:      res.TicketExpiredDate,
			ExpectedPrice:          &domain.ExpectedPrice{},
			TSMValues:              res.TSMValues,
			CommRate:               res.CommRate,
		})
		if err != nil {
			return nil, err
		}

		// Apply price service calculation before returning
		return res.FareCf, nil
	}

	if booking.Provider == enum.FlightProviderEV {
		if booking.FareData == nil {
			log.Error("totalFare nil error")
			return nil, commonErrs.ErrSomethingOccurred
		}

		fareCf, ities, err := f.evAdapter.ConfirmFare(ctx, booking)
		if err != nil {
			return nil, err
		}

		if len(booking.Itineraries) != len(ities) {
			return nil, domain.ErrItinerarySoldOut
		}

		fareCf.Currency = booking.FareData.Currency
		return fareCf, nil
	}

	// Vietnam airlines confirm
	if booking.Provider == enum.FlightProviderVNA {
		segments := []*domain.ItinerarySegment{}

		for _, itinerary := range booking.Itineraries {
			segments = append(segments, itinerary.Segments...)
		}

		vnaRS, err := f.vnaAdapter.FareCheck(ctx, booking.PassengerInfo, segments, booking.SessionID)
		if err != nil {
			log.Error("f.vnaAdapter.FareCheck error", log.Any("error", err),
				log.Any("booking.PassengerInfo", booking.PassengerInfo), log.Any("segments", segments))
			return nil, err
		}

		totalFares, err := converts.ToVNAFareConfirmFarePrice(vnaRS.PaxFares)
		if err != nil {
			log.Error("ToVNAFareConfirmFarePrice error", log.Any("error", err), log.Any("response", vnaRS.PaxFares))
			return nil, err
		}

		return totalFares, nil
	}

	// Vietjet confirm
	if booking.Provider.IsVJProvider() {
		totalFares, err := f.vjAdapter.ConfirmFare(ctx, booking, booking.Provider)
		if err != nil {
			log.Error("f.vjAdapter.ConfirmFare error", log.Any("error", err),
				log.Any("booking", booking))
			return nil, err
		}

		return totalFares, nil
	}

	// Travelfusion confirm
	if booking.Provider == enum.FlightProviderTravelFusion {
		totalFare, bkInfo, err := f.travelFusionAdapter.ConfirmFare(ctx, booking, pnr, suppliers[enum.FlightProviderTravelFusion], booking.SessionID)
		if err != nil {
			log.Error("travelFusionAdapter.ConfirmFare error", log.Any("error", err), log.Any("bk", booking), log.Any("pnr", pnr))
			return nil, err
		}

		err = f.saveBookingRef(ctx, &domain.AmadeusPNR{
			SessionID:     booking.SessionID,
			RecordLocator: bkInfo.BookingRef,
			LastTktDate:   bkInfo.LastTicketingDate,
			ExpectedPrice: bkInfo.ExpectedPrice,
		})
		if err != nil {
			return nil, err
		}

		return totalFare, nil
	}

	if booking.Provider == enum.FlightProviderEVInternational {
		res, err := f.evInternationalAdapter.ConfirmFare(ctx, booking)
		if err != nil {
			log.Error("evInternationalAdapter.ConfirmFare error", log.Any("error", err), log.Any("booking", booking))
			return nil, err
		}

		return res, nil
	}

	if booking.Provider == enum.FlightProviderVNA1A {
		res, err := f.vna1aAdapter.ConfirmFare(ctx, booking.Itineraries, booking.SessionID)
		if err != nil {
			log.Error("vna1aAdapter.ConfirmFare error", log.Any("error", err), log.Any("booking", booking))
			return nil, err
		}

		err = f.saveBookingRef(ctx, &domain.AmadeusPNR{
			SessionID:     booking.SessionID,
			RecordLocator: res.RecordLocator,
		})
		if err != nil {
			return nil, err
		}

		return res.FareCf, nil
	}

	if booking.Provider.IsHNHProvider() {
		res, bookingKey, err := f.hnhAdapter.ConfirmFare(ctx, booking.Itineraries, booking.SessionID, booking.Provider)
		if err != nil {
			log.Error("hnhAdapter.ConfirmFare error", log.Any("error", err), log.Any("booking", booking))
			return nil, err
		}

		if bookingKey != "" {
			err = f.saveBookingRef(ctx, &domain.AmadeusPNR{
				SessionID:     booking.SessionID,
				RecordLocator: bookingKey,
			})

			if err != nil {
				return nil, err
			}
		}

		return res, nil
	}

	if booking.Provider == enum.FlightProviderPkfare {
		res, err := f.pkfareAdapter.ConfirmFare(ctx, booking, booking.SessionID)
		if err != nil {
			log.Error("pkfareAdapter.ConfirmFare error", log.Any("error", err), log.Any("booking", booking))
			return nil, err
		}

		originFareCf := helpers.Copy(res).(*domain.TotalFareInfo)
		booking.OriginFareData = originFareCf

		return res, nil
	}

	if booking.Provider == enum.FlightProviderTongCheng {
		return f.internalConfirmFare(ctx, booking, pnr, suppliers, webhookCfg)
	}

	return nil, nil
}

func (h *fareService) sendWebhook(ctx context.Context, bkCode, officeID string, lastTktDate int64, transferable bool, whCfg *domain.WebhookCfg) {
	if whCfg == nil || whCfg.WebhookKey == "" || whCfg.WebhookURLCfg.LastTktDate == "" || bkCode == "" || lastTktDate == 0 {
		return
	}

	reqTrans := &domain.WebhookUpdateLastTktDate{
		OfficeID:     officeID,
		BookingCode:  bkCode,
		ExpiredAt:    lastTktDate,
		Transferable: transferable,
	}

	if err := h.webhookClient.SendUpdateLastTktDate(ctx, reqTrans, whCfg.WebhookKey, whCfg.WebhookURLCfg.LastTktDate); err != nil {
		log.Error("webhookClient.SendTransaction error", log.Any("error", err), log.Any("reqTrans", reqTrans))
	}
}

func (s *fareService) fetchAmaSSRBackgroundJob(booking *domain.BookingSession, res *domain.AmadeusCfFareRes, webhookCfg *domain.WebhookCfg) {
	go func() {
		bCtx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
		fmt.Println("Start refetch ssr")
		time.Sleep(4 * time.Minute)
		fmt.Println("Refetch ssr")
		defer cancel()

		// err := retry.Do(func() error {
		// fmt.Println("Refetch ssr", time.Now())
		lastTKT, err := s.amadesusAdapter.GetLastTktInPNR(bCtx, booking, res.RecordLocator)
		if err != nil {
			log.Error("[GetSSRInPNR] GetSSRInPNR err", log.Any("err", err))
		}

		ticketLastTKT := res.TicketExpiredDate
		if lastTKT > 0 {
			if ticketLastTKT > 0 && ticketLastTKT < lastTKT {
				lastTKT = ticketLastTKT
			} else {
				ticketLastTKT = lastTKT
			}

			if res.FareExpiredDate < lastTKT {
				lastTKT = res.FareExpiredDate
			}
		}

		fmt.Println("Fetch ssr Done", lastTKT)

		if lastTKT == 0 {
			// Notify after 4min waiting with nothing
			lastTKT = time.Now().Add(time.Duration(s.cfg.IssueDurationDefault) * time.Minute).UnixMilli()
		}

		err = s.updateLastTKTDate(bCtx, &domain.AmadeusPNR{
			SessionID:              booking.SessionID,
			RecordLocator:          res.RecordLocator,
			LastTktDate:            lastTKT,
			SkipDefaultLastTktDate: false,
			CommRate:               res.CommRate,
			TSMValues:              res.TSMValues,
			FareExpiredDate:        res.FareExpiredDate,
			TicketExpiredDate:      ticketLastTKT,
		})
		if err != nil {
			log.Error("[GetSSRInPNR] updateLastTKTDate err", log.Any("err", err))
			return
		}

		newBookingData, err := s.bookingRepo.FindBookingBySessionID(bCtx, booking.SessionID)
		if err != nil {
			log.Error("[GetSSRInPNR] FindBookingBySessionID err", log.Any("err", err))
			return
		}
		if newBookingData != nil {
			s.sendWebhook(bCtx, newBookingData.BookingCode, newBookingData.OfficeID, lastTKT, newBookingData.IsTransferable(), webhookCfg)
		}
		// }, retry.Attempts(2), retry.Delay(2*time.Minute), retry.DelayType(retry.FixedDelay))
		// if err != nil {
		// 	log.Error("[GetSSRInPNR] Retry error", log.Any("err", err))
		// }
	}()
}
