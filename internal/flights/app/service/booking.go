package service

import (
	"context"
	"errors"
	"strings"
	"time"

	"github.com/samber/lo"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client"
	hnh_client "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vietjet_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna1a_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna_client"
	convert "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

type BookingService interface {
	CreateBookingSession(ctx context.Context, req *domain.BookingSession) error
	CreateBooking(ctx context.Context, provider enum.FlightProvider, bookingDetails *domain.BookingDetails, bookingSession *domain.BookingSession, searchRequest *domain.SearchFlightsRequest, pnr *domain.PNR, sessionID string, skipInternal bool) (*domain.SvcCreateBookingResponse, error)
	CanBook(ctx context.Context, ities []*domain.FlightItinerary, listPax []*domain.PaxInfo) (bool, error)
	CancelBooking(ctx context.Context, bookingCode, sessionID, officeID string) error
	SyncBookingFlightData(ctx context.Context, bkFlight *domain.BookingSession, bkCode string, pnr []*domain.PaxInfo) (hasChanges bool, fareDataIss *domain.TotalFareInfo, err error)
	ShouldCreateInternalBooking(_ context.Context, bookingSession *domain.BookingSession) (bool, bool, error)
	IsInVJ24h(_ context.Context, bookingSession *domain.BookingSession) (bool, error)
}

type bookingService struct {
	vnaAdapter             vna_client.VNAAdapter
	bookingRepo            repositories.BookingRepository
	amadeusPNRRepo         repositories.AmadeusPNRRepository
	tfAdapter              travel_fusion.TravelFusionAdapter
	pnrRepo                repositories.PNRRepository
	amaAdapter             amadeus_client.AmadeusAdapter
	vjAdapter              vietjet_client.VietjetAdapter
	evInternationalAdapter ev_international_client.EVInternationalAdapter
	evAdapter              ev_client.EVAdapter
	ancillarySvc           AncillaryService
	vna1aAdapter           vna1a_client.VNA1AAdapter
	reportService          ReportService
	hnhAdapter             hnh_client.HNHAdapter
	pkfareAdapter          pkfare_client.PkfareAdapter
	tongChengAdapter       tongcheng_client.TongChengAdapter
	cfg                    *config.Schema
}

func NewBookingService(
	bookingRepo repositories.BookingRepository,
	vnaAdapter vna_client.VNAAdapter,
	amadeusPNRRepo repositories.AmadeusPNRRepository,
	tfAdapter travel_fusion.TravelFusionAdapter,
	pnrRepo repositories.PNRRepository,
	amaAdapter amadeus_client.AmadeusAdapter,
	vjAdapter vietjet_client.VietjetAdapter,
	evInternationalAdapter ev_international_client.EVInternationalAdapter,
	evAdapter ev_client.EVAdapter,
	ancillarySvc AncillaryService,
	vna1aAdapter vna1a_client.VNA1AAdapter,
	reportService ReportService,
	hnhAdapter hnh_client.HNHAdapter,
	pkfareAdapter pkfare_client.PkfareAdapter,
	tongChengAdapter tongcheng_client.TongChengAdapter,
	cfg *config.Schema,
) BookingService {
	return &bookingService{
		bookingRepo:            bookingRepo,
		vnaAdapter:             vnaAdapter,
		amadeusPNRRepo:         amadeusPNRRepo,
		tfAdapter:              tfAdapter,
		pnrRepo:                pnrRepo,
		amaAdapter:             amaAdapter,
		vjAdapter:              vjAdapter,
		evInternationalAdapter: evInternationalAdapter,
		evAdapter:              evAdapter,
		ancillarySvc:           ancillarySvc,
		vna1aAdapter:           vna1aAdapter,
		reportService:          reportService,
		hnhAdapter:             hnhAdapter,
		pkfareAdapter:          pkfareAdapter,
		tongChengAdapter:       tongChengAdapter,
		cfg:                    cfg,
	}
}

// SyncBookingFlightData call provider verify booking flight data with.
// Then update new time changes into bkFlight and save it to db.
// Return: true if flight time has changed
func (s *bookingService) SyncBookingFlightData(ctx context.Context, bkFlight *domain.BookingSession, bkCode string, pnr []*domain.PaxInfo) (hasChanges bool, fareDataIss *domain.TotalFareInfo, err error) {
	const byPassOfficeID = ""

	if bkFlight == nil {
		bkFlight, err = s.bookingRepo.FindOneByBookingCode(ctx, byPassOfficeID, bkCode)
		if err != nil {
			log.Error("bookingRepo.FindOneByBookingCode.bookingRepo.FindOneByBookingCode error", log.Any("error", err), log.String("bkCode", bkCode))
			return
		}
		if bkFlight == nil {
			return
		}
	}

	if bkFlight.BookingRef != "" {
		switch bkFlight.Provider {
		case enum.FlightProviderVNA1A:
			{
				hasChanges, err = s.vna1aAdapter.VerifyBookingFlightData(ctx, bkFlight)
			}
		case enum.FlightProviderAmadeus:
			{
				hasChanges, err = s.amaAdapter.VerifyBookingFlightData(ctx, bkFlight)
			}
		case enum.FlightProviderEV:
			{
				hasChanges, err = s.evAdapter.VerifyBookingFlightData(ctx, bkFlight)
			}
		case enum.FlightProviderVietJet, enum.FlightProviderVietJetAT:
			{
				hasChanges, err = s.vjAdapter.VerifyBookingFlightData(ctx, bkFlight, bkFlight.Provider)
			}
		case enum.FlightProviderTravelFusion:
			{
				hasChanges, err = s.tfAdapter.VerifyBookingFlightData(ctx, bkFlight)
			}
		case enum.FlightProviderHNH, enum.FlightProviderHNH_HPL:
			{
				hasChanges, fareDataIss, err = s.hnhAdapter.VerifyBookingFlightData(ctx, bkFlight, pnr, bkFlight.Provider)
			}
		case enum.FlightProviderEVInternational:
			{
				hasChanges, fareDataIss, err = s.evInternationalAdapter.VerifyBookingFlightData(ctx, bkFlight)
			}
		case enum.FlightProviderPkfare:
			{
				hasChanges, fareDataIss, err = s.pkfareAdapter.VerifyBookingFlightData(ctx, bkFlight)
			}
		case enum.FlightProviderTongCheng:
			{
				err = commonErrors.ErrSomethingOccurred
			}
		}
	} else {
		hasChanges = false
		fareDataIss = bkFlight.FareDataCf
	}

	if fareDataIss != nil {
		bkFlight.FareDataIss = fareDataIss
	} else {
		bkFlight.FareDataIss = bkFlight.FareDataCf
	}

	if errUpdate := s.bookingRepo.UpdateOne(ctx, bkFlight.ID, bkFlight); errUpdate != nil {
		log.Error("bookingRepo.UpdateOne error", log.Any("error", errUpdate), log.Any("bkFlight.ID", bkFlight.ID))
		err = errUpdate
		return
	}

	if err != nil {
		log.Error("VerifyBookingFlightData error", log.Any("error", err), log.Any("bkFlight.ID", bkFlight.ID))
		return
	}

	if hasChanges {
		bkFlight.Confirmed = false
		if err = s.bookingRepo.UpdateOne(ctx, bkFlight.ID, bkFlight); err != nil {
			log.Error("bookingRepo.UpdateOne error", log.Any("error", err), log.Any("bkFlight.ID", bkFlight.ID))
			return
		}
	}

	return
}

func (s *bookingService) CancelBooking(ctx context.Context, bookingCode, sessionID, officeID string) error {
	if bookingCode == "" && sessionID == "" {
		return commonErrors.ErrInvalidInput
	}

	var bk *domain.BookingSession
	var err error

	if bookingCode != "" {
		bk, err = s.bookingRepo.FindOneByBookingCode(ctx, officeID, bookingCode)
		if err != nil {
			log.Error("bookingRepo.FindOneByBookingCode error", log.Any("error", err), log.String("bookingCode", bookingCode), log.String("officeID", officeID))
			return commonErrors.ErrSomethingOccurred
		}
	} else {
		bk, err = s.bookingRepo.FindBookingBySessionID(ctx, sessionID)
		if err != nil {
			log.Error("bookingRepo.FindBookingBySessionID error", log.Any("error", err), log.String("bookingCode", bookingCode), log.String("officeID", officeID))
			return commonErrors.ErrSomethingOccurred
		}
	}

	if bk == nil {
		return commonErrors.WithMsg(commonErrors.ErrNotFound, constants.ErrMsgBookingNotFound)
	}

	if bk.TicketStatus == enum.TicketStatusOK {
		return domain.ErrTicketIssued
	}

	if bk.Status == enum.BookingStatusCancelled {
		return domain.ErrBookingCancelled
	}

	bkRef := bk.BookingRef

	if !bk.InternalBooking {
		if bkRef == "" && bk.Provider == enum.FlightProviderAmadeus {
			res, err := s.retrieveBookingRef(ctx, bk.SessionID)
			if err != nil {
				log.Error("retrieveBookingRef error", log.Any("error", err), log.String("sessionID", sessionID))
			}

			if res != nil {
				bkRef = res.BookingRef
			}
		}

		if bkRef != "" {
			partyCtx, cc := context.WithTimeout(context.Background(), constants.ThirdPartyRequestTimeout)
			defer cc()

			switch bk.Provider {
			case enum.FlightProviderAmadeus:
				{
					err := s.amaAdapter.CancelBooking(partyCtx, bkRef)
					if err != nil {
						log.Error("amaAdapter.CancelBooking", log.Any("error", err), log.String("bkRef", bkRef))
					}
				}
			case enum.FlightProviderVNA:
				{
					err := s.vnaAdapter.CancelBooking(partyCtx, bkRef)
					if err != nil {
						log.Error("vnaAdapter.CancelBooking", log.Any("error", err), log.String("bkRef", bkRef))
					}
				}
			case enum.FlightProviderVNA1A:
				{
					if err := s.vna1aAdapter.CancelBooking(partyCtx, bkRef); err != nil {
						log.Error("vna1aAdapter.CancelBooking", log.Any("error", err), log.String("bkRef", bkRef))
					}
				}
			case enum.FlightProviderPkfare:
				{
					if err := s.pkfareAdapter.CancelBooking(partyCtx, bkRef, bk.OrderNumRef); err != nil {
						log.Error("pkfareAdapter.CancelBooking", log.Any("error", err), log.String("bkRef", bkRef))
					}
				}
			}
		}
	}

	updateReq := &domain.UpdateBookingRepoRequest{
		Status: enum.BookingStatusCancelled,
	}

	bk.Status = enum.BookingStatusCancelled

	if bk.TicketStatus == enum.TicketStatusPending {
		bk.TicketStatus = enum.TicketStatusFailed

		updateReq.TicketStatus = enum.TicketStatusFailed
	}

	err = s.bookingRepo.UpdateBooking(ctx, bk.SessionID, updateReq)
	if err != nil {
		log.Error("bookingRepo.UpdateBooking error", log.Any("error", err), log.String("sessionID", bk.SessionID))
		return commonErrors.ErrSomethingOccurred
	}

	if bk.HasEMD() {
		s.ancillarySvc.ClearSeatMap(ctx, bk.Itineraries)
	}

	go func() {
		ctxBg, cancel := context.WithTimeout(context.Background(), constants.DefaultCtxTimeout)
		defer cancel()
		pnr, err := s.pnrRepo.FindOne(ctxBg, bk.SessionID)
		if err != nil {
			log.Error("pnrRepo.FindOne error", log.Any("error", err), log.String("sessionID", bk.SessionID))
		}

		err = s.reportService.SendReportBooking(ctxBg, bk, pnr)
		if err != nil {
			log.Error("reportService.SendReportBooking error", log.Any("error", err))
		}
	}()

	return nil
}

func (s *bookingService) CanBook(ctx context.Context, ities []*domain.FlightItinerary, listPax []*domain.PaxInfo) (bool, error) {
	itiIDs := []string{}

	for _, item := range ities {
		itiIDs = append(itiIDs, item.ID)
	}

	bks, err := s.bookingRepo.FindActiveBookings(ctx, itiIDs)
	if err != nil {
		log.Error("bookingRepo.FindManyByItiKeys error", log.Any("error", err), log.Any("itiIDs", itiIDs))
		return false, commonErrors.ErrSomethingOccurred
	}

	sessions := []string{}

	for _, item := range bks {
		sessions = append(sessions, item.SessionID)
	}

	pnrs, err := s.pnrRepo.FindMany(ctx, sessions)
	if err != nil {
		log.Error("pnrRepo.FindMany error", log.Any("error", err), log.Any("sessions", sessions))
		return false, commonErrors.ErrSomethingOccurred
	}

	paxNameMap := map[string]bool{}

	for _, pax := range listPax {
		fullname := helpers.GetPaxKey(pax.Surname, pax.GivenName, pax.Gender)
		paxNameMap[fullname] = true
	}

	for _, pnr := range pnrs {
		for _, pax := range pnr.ListPax {
			fullname := helpers.GetPaxKey(pax.Surname, pax.GivenName, pax.Gender)
			if paxNameMap[fullname] {
				return false, nil
			}
		}
	}

	return true, nil
}

func (s *bookingService) retrieveBookingRef(ctx context.Context, sessionID string) (*domain.SvcCreateBookingResponse, error) {
	amadeusPNR, err := s.amadeusPNRRepo.FindOne(ctx, sessionID)
	if err != nil {
		log.Error("s.amadeusPNRRepo.FindOne error", log.Any("err", err), log.String("sessionID", sessionID))
		return nil, commonErrors.ErrSomethingOccurred
	}

	if amadeusPNR == nil {
		log.Error("amadeusPNR nil", log.String("sessionID", sessionID))
		return nil, commonErrors.ErrSomethingOccurred
	}

	return &domain.SvcCreateBookingResponse{
		BookingRef:             amadeusPNR.RecordLocator,
		LastTicketingDate:      amadeusPNR.LastTktDate,
		SkipDefaultLastTktDate: amadeusPNR.SkipDefaultLastTktDate,
		ExpectedPrice:          amadeusPNR.ExpectedPrice,
		CommRate:               amadeusPNR.CommRate,
		FareExpiredDate:        amadeusPNR.FareExpiredDate,
		TicketExpiredDate:      amadeusPNR.TicketExpiredDate,
	}, nil
}

func (s *bookingService) ShouldCreateInternalBooking(_ context.Context, bookingSession *domain.BookingSession) (bool, bool, error) {
	internalBookingProviders := []enum.FlightProvider{
		enum.FlightProviderHNH,
		enum.FlightProviderAmadeus,
		enum.FlightProviderPkfare,
		enum.FlightProviderTongCheng,
	}

	internalBookingProviders = append(internalBookingProviders, enum.ListFlightProviderVJ...)

	if !lo.Contains(internalBookingProviders, bookingSession.Provider) {
		return false, false, nil
	}

	internalBookingOffices := strings.Split(s.cfg.InternalBookingOffices, ",")
	for i := 0; i < len(internalBookingOffices); i++ {
		internalBookingOffices[i] = strings.TrimSpace(internalBookingOffices[i])
	}

	isInVJ24h, err := s.IsInVJ24h(context.TODO(), bookingSession)
	if err != nil {
		return false, false, err
	}

	isInternalBooking := lo.Contains(internalBookingOffices, bookingSession.OfficeID)
	if isInternalBooking {
		return bookingSession.FlightType == enum.FlightTypeInternational, isInVJ24h, nil
	}

	return isInternalBooking, isInVJ24h, nil
}

func (s *bookingService) IsInVJ24h(_ context.Context, bookingSession *domain.BookingSession) (bool, error) {
	VJProviders := []enum.FlightProvider{enum.FlightProviderPkfare, enum.FlightProviderTongCheng}

	VJProviders = append(VJProviders, enum.ListFlightProviderVJ...)
	VJProviders = append(VJProviders, enum.ListFlightProviderHNH...)

	firstFlight := bookingSession.Itineraries[0]
	const appliedAirline = "VJ"

	return lo.Contains(VJProviders, bookingSession.Provider) && firstFlight.CarrierMarketing == appliedAirline && time.Until(firstFlight.DepartDt) <= time.Hour*24, nil
}

func (s *bookingService) CreateBooking(ctx context.Context, provider enum.FlightProvider, bookingDetails *domain.BookingDetails, bookingSession *domain.BookingSession, searchRequest *domain.SearchFlightsRequest, pnr *domain.PNR, sessionID string, skipInternal bool) (*domain.SvcCreateBookingResponse, error) {
	shouldCreateInternalBooking, isInVJ24h, _ := s.ShouldCreateInternalBooking(ctx, bookingSession)
	bookingSession.IsVJ24h = isInVJ24h

	// shouldCreateInternalBooking: officeID book ảo && chuyến quốc tế
	// isInVJ24h: Provider HNH/VJ + hãng VJ + bay trong ngày

	if (shouldCreateInternalBooking || isInVJ24h) && !skipInternal {
		bookingSession.InternalBooking = true

		return &domain.SvcCreateBookingResponse{
			LastTicketingDate: time.Now().Add(constants.InternalBookingLastTktDate).UnixMilli(),
		}, nil
	}

	// Call to provider to create booking
	switch provider {
	case enum.FlightProviderAmadeus, enum.FlightProviderTravelFusion:
		return s.retrieveBookingRef(ctx, sessionID)
	case enum.FlightProviderVNA:
		req, err := convert.ToVNABookingRQ(bookingDetails)
		if err != nil {
			log.Error("convert.ToVNABookingRQ error", log.Any("err", err), log.Any("bookingDetails", bookingDetails))
			return nil, err
		}

		response, err := s.vnaAdapter.CreateBooking(ctx, req, sessionID)
		if err != nil {
			if errors.Is(err, domain.ErrSeatNotAvailable) {
				_ = s.ancillarySvc.ClearSeatMap(ctx, bookingDetails.Itineraries)
			}
			log.Error("s.vnaAdapter.CreateBooking error", log.Any("err", err), log.Any("sessionID", sessionID))
			return nil, err
		}

		if bookingSession.HasEMD() {
			_ = s.ancillarySvc.ClearSeatMap(ctx, bookingDetails.Itineraries)
		}

		var lastTicketingDate int64
		if response.TicketTimeLimit != nil {
			lastTicketingDate = response.TicketTimeLimit.UnixMilli()
		}

		return &domain.SvcCreateBookingResponse{
			BookingRef:        response.LocatorCode,
			LastTicketingDate: lastTicketingDate,
			SeatError:         err,
		}, nil
	case enum.FlightProviderVietJet, enum.FlightProviderVietJetAT:
		res, paxFareInfos, err := s.vjAdapter.Booking(ctx, bookingDetails, searchRequest, pnr, provider, sessionID)
		if err != nil {
			log.Error("s.vjAdapter.CreateBooking error", log.Any("err", err), log.Any("bookingDetails", bookingDetails), log.Any("searchRequest", searchRequest))
			return nil, err
		}

		if len(paxFareInfos) > 0 {
			bookingSession.FareDataCf.PaxFareInfos = paxFareInfos
			err = s.bookingRepo.UpdateFareDataCf(ctx, bookingSession.SessionID, bookingSession.FareDataCf, bookingSession.OriginFareData)
			if err != nil {
				log.Error("s.bookingRepo.UpdateFareDataCf error", log.Any("err", err), log.Any("fareDataCf", bookingSession.FareDataCf), log.Any("searchRequest", searchRequest))
				return nil, err
			}
		}

		return res, nil
	case enum.FlightProviderEVInternational:
		res, err := s.evInternationalAdapter.Booking(ctx, bookingDetails, searchRequest, sessionID)
		if err != nil {
			log.Error("s.evInternationalAdapter.CreateBooking error", log.Any("err", err), log.Any("bookingDetails", bookingDetails), log.Any("searchRequest", searchRequest))
			return nil, err
		}

		return res, nil
	case enum.FlightProviderEV:
		fareCf, ities, err := s.evAdapter.ConfirmFare(ctx, bookingSession)
		if err != nil {
			return nil, err
		}

		if len(bookingSession.Itineraries) != len(ities) {
			return nil, domain.ErrItinerarySoldOut
		}

		if fareCf.BaseTotalFareAmount > bookingDetails.FareDataCf.BaseTotalFareAmount {
			return nil, domain.ErrTicketFareChanged
		}

		response, err := s.evAdapter.CreateBooking(ctx, bookingDetails, searchRequest, ities, sessionID)
		if err != nil {
			log.Error("s.evAdapter.CreateBooking error", log.Any("err", err), log.Any("sessionID", sessionID))
			return nil, err
		}

		return response, nil
	case enum.FlightProviderVNA1A:
		verifySession, err := s.retrieveBookingRef(ctx, sessionID)
		if err != nil {
			return nil, err
		}

		res, err := s.vna1aAdapter.CreateBooking(ctx, bookingDetails, verifySession.BookingRef, sessionID)
		if err != nil {
			log.Error("s.vna1aAdapter.CreateBooking error", log.Any("err", err), log.String("verifySession.BookingRef", verifySession.BookingRef), log.Any("sessionID", sessionID))
			return nil, err
		}

		return res, nil
	case enum.FlightProviderHNH, enum.FlightProviderHNH_HPL:
		pnrData, err := s.amadeusPNRRepo.FindOne(ctx, sessionID)
		verifySession := ""

		if err != nil {
			log.Error("s.amadeusPNRRepo.FindOne error", log.Any("err", err), log.String("sessionID", sessionID))
			return nil, commonErrors.ErrSomethingOccurred
		}

		if pnrData != nil {
			verifySession = pnrData.RecordLocator
		}

		res, err := s.hnhAdapter.CreateBooking(ctx, bookingSession, pnr, verifySession, sessionID, provider)
		if err != nil {
			log.Error("s.hnhAdapter.CreateBooking error", log.Any("err", err), log.String("verifySession.BookingRef", verifySession), log.Any("sessionID", sessionID))
			return nil, err
		}

		return res, nil
	case enum.FlightProviderPkfare:
		{
			res, err := s.pkfareAdapter.CreateBooking(ctx, bookingDetails, sessionID)
			if err != nil {
				log.Error("s.pkfareAdapter.CreateBooking error", log.Any("err", err), log.Any("sessionID", sessionID))
				return nil, err
			}

			return res, nil
		}
	case enum.FlightProviderTongCheng:
		{
			res, err := s.pkfareAdapter.CreateBooking(ctx, bookingDetails, sessionID)
			if err != nil {
				log.Error("s.pkfareAdapter.CreateBooking error", log.Any("err", err), log.Any("sessionID", sessionID))
				return nil, err
			}

			return res, nil
		}
	default:
		log.Error("Unsupported provider error", log.Int("type", int(provider)))
		return nil, commonErrors.ErrInvalidInput
	}
}

func (s *bookingService) CreateBookingSession(ctx context.Context, req *domain.BookingSession) error {
	for _, iti := range req.Itineraries {
		iti.GenerateItiID()
	}

	err := s.bookingRepo.InsertOne(ctx, req)
	if err != nil {
		log.Error("bookingRepo.InsertOne error", log.Any("error", err), log.Any("req", req))
		return commonErrors.ErrSomethingOccurred
	}

	return nil
}
