package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	commonErrors "gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	redisRepos "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vietjet_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna_client"
	convert "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

type AncillaryService interface {
	GetSeatMap(ctx context.Context, searchCached *domain.SearchFlightsCachedRecord, flightResponse *domain.ResponseFlight, seatmapReq *domain.GetSeatMapReq) (*domain.GetSeatMapRes, error)
	ClearSeatMap(ctx context.Context, itineraries []*domain.FlightItinerary) error
	GetBaggageOptions(ctx context.Context, searchCached *domain.SearchFlightsCachedRecord, flightResponse *domain.ResponseFlight, seatmapReq *domain.GetBaggageOptionsRequest) ([]*domain.BaggageOption, error)
}

type ancillaryService struct {
	amadeusAdapter     amadeus_client.AmadeusAdapter
	vietjetAdapter     vietjet_client.VietjetAdapter
	vnaAdapter         vna_client.VNAAdapter
	tfAdapter          travel_fusion.TravelFusionAdapter
	aircraftLayoutRepo repositories.AircraftLayoutRepository
	seatMapRepo        repositories.SeatMapRepository
	baggageRepo        repositories.BaggageOptionRepository
	ancillaryRedisRepo redisRepos.AncillaryRepository
	supplierService    SupplierRouteService
	listFlightSvc      ListFlightService
	currencyExSvc      CurrencyExchangeService
	hnhAdapter         hnh_client.HNHAdapter
}

func NewAncillaryService(
	amadeusAdapter amadeus_client.AmadeusAdapter,
	vietjetAdapter vietjet_client.VietjetAdapter,
	vnaAdapter vna_client.VNAAdapter,
	tfAdapter travel_fusion.TravelFusionAdapter,
	aircraftLayoutRepo repositories.AircraftLayoutRepository,
	seatMapRepo repositories.SeatMapRepository,
	baggageRepo repositories.BaggageOptionRepository,
	ancillaryRedisRepo redisRepos.AncillaryRepository,
	supplierService SupplierRouteService,
	listFlightSvc ListFlightService,
	currencyExSvc CurrencyExchangeService,
	hnhAdapter hnh_client.HNHAdapter,
) AncillaryService {
	return &ancillaryService{amadeusAdapter, vietjetAdapter, vnaAdapter, tfAdapter, aircraftLayoutRepo, seatMapRepo, baggageRepo, ancillaryRedisRepo, supplierService, listFlightSvc, currencyExSvc, hnhAdapter}
}

func (s *ancillaryService) GetSeatMap(ctx context.Context, searchCached *domain.SearchFlightsCachedRecord, flightResponse *domain.ResponseFlight, seatmapReq *domain.GetSeatMapReq) (*domain.GetSeatMapRes, error) {
	var (
		err                        error
		res                        *domain.GetSeatMapRes
		seatSegments               []*domain.SeatSegment
		seatMapKey, seatMapLockKey string
		segmentIndex               int
		itinerary                  *domain.FlightItinerary
		lock                       bool
	)

	for _, itin := range flightResponse.Itineraries {
		if itin.ID == seatmapReq.ItineraryID {
			for _, segment := range itin.Segments {
				if segment.Index == seatmapReq.SegmentIndex {
					seatMapKey = segment.GenerateSegmentID()
					seatSegmentsRepo, err := s.seatMapRepo.FindSeatMapByKey(ctx, seatMapKey, time.Now().UnixMilli())
					if err != nil {
						log.Error("s.seatMapRepo.FindSeatMapByKey err", log.Any("err", err))
						return nil, err
					}

					if seatSegmentsRepo != nil {
						seatSegmentsRepo.SegmentIndex = segment.Index
						seatSegmentsRepo.ItineraryID = itin.ID // luu cached theo key cua segment nen neu itinery khac van lay chung seatmap dc

						return &domain.GetSeatMapRes{
							SeatSegments: []*domain.SeatSegment{seatSegmentsRepo},
							ErrorRes: domain.ErrorRes{
								IsSuccess: true,
							},
						}, nil
					}

					segmentIndex = segment.Index
					itinerary = itin
					seatSegments = []*domain.SeatSegment{{
						Key:          seatMapKey,
						ItineraryID:  itin.ID,
						SegmentIndex: segmentIndex,
						SeatOptions:  []*domain.SeatOption{},
						ExpiredAt:    time.Now().Add(constants.SeatMapExpireTime).UnixMilli(),
					}}

					break
				}
			}
		}
	}
	tracingID := fmt.Sprintf("%s|%s|%s|%d", seatmapReq.FlightID, seatmapReq.ItineraryID, seatMapKey, seatmapReq.SegmentIndex)
	switch searchCached.Provider {
	case enum.FlightProviderTravelFusion:
		seatMapLockKey = s.genGetSeatMapLockKey(seatMapKey)
	default:
		seatMapLockKey = s.genTFGetAncillaryKey(flightResponse.FlightID)
	}

	defer func() {
		if lock {
			s.ancillaryRedisRepo.ReleaseLock(seatMapLockKey)
		}
	}()

	res = &domain.GetSeatMapRes{
		SeatSegments: seatSegments,
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
	}

	if seatMapKey == "" {
		return nil, domain.ErrSegmentNotFound
	}

	lock, err = s.ancillaryRedisRepo.AcquireLock(seatMapLockKey)
	if err != nil {
		log.Error("ancillaryRedisRepo.AcquireLock error", log.Any("error", err), log.String("seatMapLockKey", seatMapLockKey))
		return nil, err
	}

	if !lock {
		// get seatMap option cache
		seatSegmentsRepo, err := s.getCacheSeatMap(ctx, seatmapReq, seatMapKey, seatMapLockKey)
		if err != nil {
			log.Error("s.getCacheBaggage err", log.Any("error", err), log.Any("seatmapReq", seatmapReq))
			return nil, err
		}

		if seatSegmentsRepo != nil {
			seatSegmentsRepo.SegmentIndex = seatmapReq.SegmentIndex
			seatSegmentsRepo.ItineraryID = seatmapReq.ItineraryID // luu cached theo key cua segment nen neu itinery khac van lay chung seatmap dc

			return &domain.GetSeatMapRes{
				SeatSegments: []*domain.SeatSegment{seatSegmentsRepo},
				ErrorRes: domain.ErrorRes{
					IsSuccess: true,
				},
			}, nil
		}
	}

	// searchKey = searchCached.Key
	switch searchCached.Provider {
	// case enum.FlightProviderVietJet:
	// 	aircraftLayouts, err := s.aircraftLayoutRepo.FindAircraftLayouts(ctx, itinerary.CarrierMarketing)
	// 	if err != nil {
	// 		log.Error("s.aircraftLayoutRepo err", log.Any("err", err))
	// 		return nil, commonErrors.ErrSomethingOccurred
	// 	}

	// 	seatSegments, err = s.vietjetAdapter.GetSeatMap(ctx, searchKey, segmentIndex, itinerary, aircraftLayouts, seatMapKey)
	// 	if err != nil {
	// 		log.Error("s.vietjetAdapter.GetSeatMap err", log.Any("err", err))
	// 		return nil, err
	// 	}

	// 	convert.GenerateSeatMapAisle(seatSegments)
	// case enum.FlightProviderVNA:
	// 	if itinerary == nil || len(itinerary.Segments) < segmentIndex {
	// 		return nil, commonErrors.ErrInvalidInput
	// 	}

	// 	segment := itinerary.Segments[segmentIndex-1]
	// 	segmentSeat, err := s.vnaAdapter.GetSeatMap(ctx, segment, tracingID)
	// 	if err != nil {
	// 		log.Error("s.vnaAdapter.GetSeatMap err", log.Any("err", err))
	// 		return res, nil
	// 	}

	// 	if segmentSeat != nil {
	// 		seatSegments = []*domain.SeatSegment{segmentSeat}
	// 		convert.GenerateSeatMapAisle(seatSegments)
	// 	}

	// 	res = &domain.GetSeatMapRes{
	// 		SeatSegments: seatSegments,
	// 		ErrorRes: domain.ErrorRes{
	// 			IsSuccess: true,
	// 		},
	// 	}
	// TODO Reopen when ready
	// case enum.FlightProviderAmadeus:
	// 	if itinerary == nil || len(itinerary.Segments) < segmentIndex {
	// 		return nil, commonErrors.ErrInvalidInput
	// 	}

	// 	segment := itinerary.Segments[segmentIndex-1]
	// 	segmentSeat, err := s.amadeusAdapter.GetSeatMapStateless(ctx, segment, tracingID)
	// 	if err != nil {
	// 		log.Error("s.amadeusAdapter.GetSeatMapStateless err", log.Any("err", err), log.Any("segment", segment))
	// 		return res, nil
	// 	}

	// 	if segmentSeat != nil {
	// 		seatSegments = []*domain.SeatSegment{segmentSeat}
	// 		convert.GenerateSeatMapAisle(seatSegments)
	// 	}

	// 	res = &domain.GetSeatMapRes{
	// 		SeatSegments: seatSegments,
	// 		ErrorRes: domain.ErrorRes{
	// 			IsSuccess: true,
	// 		},
	// 	}
	case enum.FlightProviderTravelFusion:
		seatOptions, _, err := s.getTFAncillaryOptions(ctx, searchCached, flightResponse, itinerary, tracingID, false)
		if err != nil {
			log.Error(" s.getTFAncillaryOptions error", log.Any("error", err))
			return res, nil
		}

		if len(seatOptions) > 0 {
			for _, seatSegment := range seatOptions {
				if seatSegment.ItineraryID == seatmapReq.ItineraryID && seatSegment.SegmentIndex == seatmapReq.SegmentIndex {
					res = &domain.GetSeatMapRes{
						SeatSegments: []*domain.SeatSegment{seatSegment},
						ErrorRes: domain.ErrorRes{
							IsSuccess: true,
						},
					}

					return res, nil
				}
			}
		}
	case enum.FlightProviderHNH, enum.FlightProviderHNH_HPL:
		seatOptions, err := s.hnhAdapter.GetSeatMap(ctx, flightResponse.Itineraries, tracingID, searchCached.Provider)
		if err != nil {
			log.Error(" s.hnhAdapter.GetSeatMap error", log.Any("error", err))
			return res, nil
		}

		if len(seatOptions) > 0 {
			convert.GenerateSeatMapAisle(seatOptions)
			seatSegments = seatOptions
		}
	case enum.FlightProviderTongCheng:
		{
			return res, commonErrors.ErrSomethingOccurred
		}
	}

	err = s.cacheSeatMap(ctx, seatSegments)
	if err != nil {
		log.Error("s.cacheSeatMap err", log.Any("error", err))
		return nil, err
	}

	for _, seatSegment := range seatSegments {
		if seatSegment.ItineraryID == seatmapReq.ItineraryID && seatSegment.SegmentIndex == seatmapReq.SegmentIndex {
			res = &domain.GetSeatMapRes{
				SeatSegments: []*domain.SeatSegment{seatSegment},
				ErrorRes: domain.ErrorRes{
					IsSuccess: true,
				},
			}

			break
		}
	}

	return res, nil
}

func (s *ancillaryService) ClearSeatMap(ctx context.Context, itineraries []*domain.FlightItinerary) error {
	keys := []string{}
	for _, iti := range itineraries {
		for _, segment := range iti.Segments {
			keys = append(keys, segment.GenerateSegmentID())
		}
	}

	return s.seatMapRepo.ClearSeatMapByKeys(ctx, keys)
}

func (s *ancillaryService) GetBaggageOptions(ctx context.Context, searchCached *domain.SearchFlightsCachedRecord, flightResponse *domain.ResponseFlight, req *domain.GetBaggageOptionsRequest) ([]*domain.BaggageOption, error) {
	var (
		err                  error
		hasFoundItiID, lock  bool
		baggages             = []*domain.BaggageOption{}
		tracingID            = fmt.Sprintf("%s|%s", req.FlightID, req.ItineraryID)
		baggageOptionCache   = []*domain.BaggageOptionCache{}
		baggageOptionLockKey string
	)

	switch searchCached.Provider {
	case enum.FlightProviderTravelFusion:
		baggageOptionLockKey = s.genTFGetAncillaryKey(req.FlightID) // because tf get baggage option full flight
	default:
		baggageOptionLockKey = s.genGetSeatMapLockKey(req.ItineraryID)
	}

	defer func() {
		if lock {
			s.ancillaryRedisRepo.ReleaseLock(baggageOptionLockKey)
		}
	}()

	for _, itinerary := range flightResponse.Itineraries {
		if itinerary.ID == req.ItineraryID {
			hasFoundItiID = true
			lock, err = s.ancillaryRedisRepo.AcquireLock(baggageOptionLockKey)
			if err != nil {
				return nil, err
			}

			if !lock {
				// get baggage option cache
				baggageOption, err := s.getCacheBaggage(ctx, req.FlightID, req.ItineraryID, baggageOptionLockKey)
				if err != nil {
					log.Error("s.getCacheBaggage err", log.Any("error", err), log.Any("req", req))
					return nil, err
				}

				if baggageOption != nil {
					return baggageOption.BaggageOptions, nil
				}
			}

			switch searchCached.Provider {
			// case enum.FlightProviderVNA:
			// 	baggages, err = s.getVNABaggageOptions(ctx, itinerary, tracingID)
			// 	if err != nil {
			// 		log.Error(" s.getVNABaggageOptions error", log.Any("error", err), log.String("tracingID", tracingID), log.Any("req", req))
			// 		return nil, commonErrors.ErrSomethingOccurred
			// 	}

			// 	baggageOptionCache = append(baggageOptionCache, &domain.BaggageOptionCache{
			// 		FlightID:       flightResponse.FlightID,
			// 		ItineraryID:    itinerary.ID,
			// 		BaggageOptions: baggages,
			// 		ExpiredAt:      flightResponse.Itineraries[0].DepartDt.Add(-constants.BaggageBookingDeadlineTime).UnixMilli(),
			// 	})
			case enum.FlightProviderHNH, enum.FlightProviderHNH_HPL:
				baggages, err = s.hnhAdapter.GetBaggageOption(ctx, []*domain.FlightItinerary{itinerary}, tracingID, searchCached.Provider)
				if err != nil {
					log.Error(" s.hnhAdapter error", log.Any("error", err), log.String("tracingID", tracingID), log.Any("req", req))
					return nil, commonErrors.ErrSomethingOccurred
				}

				baggageOptionCache = append(baggageOptionCache, &domain.BaggageOptionCache{
					FlightID:       flightResponse.FlightID,
					ItineraryID:    itinerary.ID,
					BaggageOptions: baggages,
					ExpiredAt:      flightResponse.Itineraries[0].DepartDt.Add(-constants.BaggageBookingDeadlineTime).UnixMilli(),
				})
			case enum.FlightProviderTravelFusion:
				_, temmpBaggage, err := s.getTFAncillaryOptions(ctx, searchCached, flightResponse, itinerary, tracingID, true)
				if err != nil {
					log.Error(" s.getTFAncillaryOptions error", log.Any("error", err), log.String("tracingID", tracingID), log.Any("req", req))
					return nil, err
				}

				for itineraryIndex, baggage := range temmpBaggage {
					if itineraryIndex == itinerary.Index {
						return baggage, nil
					}
				}
				// case enum.FlightProviderAmadeus:
				// 	baggages, err = s.amadeusAdapter.GetBaggage(ctx, searchCached, itinerary, tracingID)
				// 	if err != nil {
				// 		log.Error(" s.amadeusAdapter.GetBaggage error", log.Any("error", err), log.String("tracingID", tracingID), log.Any("req", req))
				// 		return nil, commonErrors.ErrSomethingOccurred
				// 	}

				// 	baggageOptionCache = append(baggageOptionCache, &domain.BaggageOptionCache{
				// 		FlightID:       flightResponse.FlightID,
				// 		ItineraryID:    itinerary.ID,
				// 		BaggageOptions: baggages,
				// 		ExpiredAt:      flightResponse.Itineraries[0].DepartDt.Add(-constants.BaggageBookingDeadlineTime).UnixMilli(),
				// 	})
			case enum.FlightProviderTongCheng:
				return nil, commonErrors.ErrSomethingOccurred
			}

			break
		}
	}

	if !hasFoundItiID {
		return nil, domain.ErrItineraryNotFound
	}

	go func() {
		ctxRepo, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
		defer cc()
		err := s.cacheBaggage(ctxRepo, baggageOptionCache)
		if err != nil {
			log.Error("s.cacheBaggage err", log.Any("error", err))
		}
	}()

	return baggages, err
}

func (s *ancillaryService) genGetSeatMapLockKey(seatmapKey string) string {
	prefix := "get-seatmap"

	return fmt.Sprintf("%s-%s", prefix, seatmapKey)
}

func (s *ancillaryService) genTFGetAncillaryKey(key string) string {
	prefix := "travel-fusion-get-ancillary"

	return fmt.Sprintf("%s-%s", prefix, key)
}

// func (s *ancillaryService) getVNABaggageOptions(ctx context.Context, itinerary *domain.FlightItinerary, tracingID string) ([]*domain.BaggageOption, error) {
// 	res := []*domain.BaggageOption{}

// 	baggages, err := s.vnaAdapter.GetBaggages(ctx, itinerary, tracingID)
// 	if err != nil {
// 		log.Error("vnaAdapter.GetBaggageOptions error", log.Any("error", err))
// 		return nil, err
// 	}

// 	res = append(res, baggages...)

// 	return res, nil
// }

func (s *ancillaryService) cacheSeatMap(ctx context.Context, seatSegments []*domain.SeatSegment) error {
	err := s.seatMapRepo.UpsertMany(ctx, seatSegments)
	if err != nil {
		log.Error("seatMapRepo.UpsertMany error", log.Any("error", err))
		return err
	}

	return nil
}

func (s *ancillaryService) cacheBaggage(ctx context.Context, items []*domain.BaggageOptionCache) error {
	err := s.baggageRepo.UpsertMany(ctx, items)
	if err != nil {
		log.Error("baggageRepo.UpsertMany error", log.Any("error", err))
		return err
	}

	return nil
}

func (s *ancillaryService) getCacheBaggage(ctx context.Context, flightID, itineraryID, baggageOptionLockKey string) (*domain.BaggageOptionCache, error) {
	err := s.ancillaryRedisRepo.WaitForLockToRelease(baggageOptionLockKey, time.Second, 60)
	if err != nil {
		log.Error("WaitForLockToRelease error", log.Any("error", err), log.String("baggageOptionLockKey", baggageOptionLockKey))
		return nil, err
	}

	baggageOption, err := s.baggageRepo.FindByItineraryId(ctx, flightID, itineraryID, time.Now().UnixMilli())
	if err != nil {
		log.Error("s.baggageRepo.FindByItineraryId err", log.Any("err", err), log.String("flightID", flightID), log.String("itineraryID", itineraryID))
		return nil, err
	}

	return baggageOption, nil
}

func (s *ancillaryService) getCacheSeatMap(ctx context.Context, _ *domain.GetSeatMapReq, seatMapKey, seatMapLockKey string) (*domain.SeatSegment, error) {
	err := s.ancillaryRedisRepo.WaitForLockToRelease(seatMapLockKey, time.Second, 60)
	if err != nil {
		log.Error("WaitForLockToRelease error", log.Any("error", err), log.String("seatMapLockKey", seatMapLockKey))
		return nil, err
	}

	seatSegmentsRepo, err := s.seatMapRepo.FindSeatMapByKey(ctx, seatMapKey, time.Now().UnixMilli())
	if err != nil {
		log.Error("s.seatMapRepo.FindSeatMapByKey err", log.Any("err", err))
		return nil, err
	}

	return seatSegmentsRepo, nil
}

func (s *ancillaryService) mapSeatMapCurrencyToVND(ctx context.Context, seatSegments []*domain.SeatSegment, provider enum.FlightProvider) ([]*domain.SeatSegment, error) {
	if len(seatSegments) == 0 {
		return nil, nil
	}

	rateMap, err := s.currencyExSvc.GetRateMapping(ctx, constants.VNCurrency, provider)
	if err != nil {
		log.Error("currencyExSvc.GetRaeMapping error", log.Any("error", err))
		return nil, err
	}

	for _, seatSegment := range seatSegments {
		for _, seatOption := range seatSegment.SeatOptions {
			for _, seatRow := range seatOption.Rows {
				for _, facility := range seatRow.Facilities {
					if facility.SeatCharge != nil {
						facility.SeatCharge.BaseAmount = commonHelpers.CeilFloat(rateMap[facility.SeatCharge.Currency]*facility.SeatCharge.BaseAmount, -3)
						facility.SeatCharge.TaxAmount = commonHelpers.CeilFloat(rateMap[facility.SeatCharge.Currency]*facility.SeatCharge.TaxAmount, -3)
						facility.SeatCharge.TotalAmount = commonHelpers.CeilFloat(rateMap[facility.SeatCharge.Currency]*facility.SeatCharge.TotalAmount, -3)
						facility.SeatCharge.Currency = constants.VNCurrency
					}
				}
			}
		}
	}

	return seatSegments, nil
}

func (s *ancillaryService) mapBaggageCurrencyToVND(ctx context.Context, baggageOptions []*domain.BaggageOption, provider enum.FlightProvider) ([]*domain.BaggageOption, error) {
	if len(baggageOptions) == 0 {
		return nil, nil
	}

	rateMap, err := s.currencyExSvc.GetRateMapping(ctx, constants.VNCurrency, provider)
	if err != nil {
		log.Error("currencyExSvc.GetRaeMapping error", log.Any("error", err))
		return nil, err
	}

	for _, baggageOption := range baggageOptions {
		if baggageOption.TotalBaggageCharge != nil {
			baggageOption.TotalBaggageCharge.BaseAmount = commonHelpers.CeilFloat(rateMap[baggageOption.TotalBaggageCharge.Currency]*baggageOption.TotalBaggageCharge.BaseAmount, -3)
			baggageOption.TotalBaggageCharge.TaxAmount = commonHelpers.CeilFloat(rateMap[baggageOption.TotalBaggageCharge.Currency]*baggageOption.TotalBaggageCharge.TaxAmount, -3)
			baggageOption.TotalBaggageCharge.TotalAmount = commonHelpers.CeilFloat(rateMap[baggageOption.TotalBaggageCharge.Currency]*baggageOption.TotalBaggageCharge.TotalAmount, -3)
			baggageOption.TotalBaggageCharge.Currency = constants.VNCurrency
		}
	}

	return baggageOptions, nil
}

// DEPRECATED: isBaggageAncillary se duoc thay the bo enum ancillary
func (s *ancillaryService) getTFAncillaryOptions(ctx context.Context, searchCached *domain.SearchFlightsCachedRecord, flightResponse *domain.ResponseFlight, itinerary *domain.FlightItinerary, tracingID string, isBaggageAncillary bool) ([]*domain.SeatSegment, map[int][]*domain.BaggageOption, error) {
	var (
		seatSegments                    []*domain.SeatSegment
		mapItineraryIndexBaggageOptions map[int][]*domain.BaggageOption
	)
	suppliers, err := s.supplierService.GetSuppliersByRoute(ctx, itinerary.DepartPlace, itinerary.ArrivalPlace)
	if err != nil {
		log.Error("supplierService.GetSuppliersByRoute error", log.Any("error", err))
		return nil, nil, err
	}

	checkFareInfo, err := s.tfAdapter.CheckFare(ctx, searchCached.Request, flightResponse.Itineraries, suppliers[enum.FlightProviderTravelFusion], tracingID)
	if err != nil {
		if errors.Is(err, domain.ErrItinerarySoldOut) {
			go func() {
				ctxSvc, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
				defer cc()
				_ = s.listFlightSvc.SetFlightSoldOut(ctxSvc, flightResponse.FlightID)
			}()

			return nil, nil, domain.ErrItinerarySoldOut
		}

		log.Error("tfAdapter.CheckFare error", log.Any("error", err))

		return nil, nil, err
	}

	if len(checkFareInfo.SegmentSeats) > 0 {
		seatSegments, err = s.mapSeatMapCurrencyToVND(ctx, checkFareInfo.SegmentSeats, enum.FlightProviderTravelFusion)
		if err != nil {
			log.Error("tfAdapter.mapSeatMapCurrencyToVND error", log.Any("error", err))
			return nil, nil, err
		}

		convert.GenerateSeatMapAisle(seatSegments)

		go func() {
			ctxRepo, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
			defer cc()
			err := s.cacheSeatMap(ctxRepo, seatSegments)
			if err != nil {
				log.Error("s.cacheSeatMap err", log.Any("error", err))
			}
		}()
	}

	if len(checkFareInfo.Baggages) > 0 && isBaggageAncillary {
		mapItineraryIndexBaggageOptions = make(map[int][]*domain.BaggageOption)
		checkFareInfo.Baggages, err = s.mapBaggageCurrencyToVND(ctx, checkFareInfo.Baggages, enum.FlightProviderTravelFusion)
		if err != nil {
			log.Error("s.mapBaggageCurrencyToVND error", log.Any("error", err))
			return nil, nil, err
		}

		for _, baggageOptions := range checkFareInfo.Baggages {
			if mapItineraryIndexBaggageOptions[baggageOptions.ItineraryIndex] == nil {
				mapItineraryIndexBaggageOptions[baggageOptions.ItineraryIndex] = make([]*domain.BaggageOption, 0)
				mapItineraryIndexBaggageOptions[baggageOptions.ItineraryIndex] = append(mapItineraryIndexBaggageOptions[baggageOptions.ItineraryIndex], baggageOptions)
			} else {
				mapItineraryIndexBaggageOptions[baggageOptions.ItineraryIndex] = append(mapItineraryIndexBaggageOptions[baggageOptions.ItineraryIndex], baggageOptions)
			}
		}

		baggageOptionCache := []*domain.BaggageOptionCache{}

		for _, itinerary := range flightResponse.Itineraries {
			tempBaggageOptionCache := &domain.BaggageOptionCache{
				FlightID:    flightResponse.FlightID,
				ItineraryID: itinerary.ID,
				ExpiredAt:   flightResponse.Itineraries[0].DepartDt.Add(-constants.BaggageBookingDeadlineTime).UnixMilli(),
			}

			if mapItineraryIndexBaggageOptions[itinerary.Index] != nil {
				tempBaggageOptionCache.BaggageOptions = mapItineraryIndexBaggageOptions[itinerary.Index]
			}

			baggageOptionCache = append(baggageOptionCache, tempBaggageOptionCache)
		}

		// Cached baggage
		go func() {
			ctxRepo, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
			defer cc()
			err = s.cacheBaggage(ctxRepo, baggageOptionCache)
			if err != nil {
				log.Error("s.cacheSeatMap err", log.Any("error", err))
			}
		}()
	}

	return seatSegments, mapItineraryIndexBaggageOptions, nil
}
