package command

import (
	"context"
	"errors"
	"net/http"
	"time"

	"gitlab.deepgate.io/apps/common/auth"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/order"
	redisRepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

type CreateBookingHandler interface {
	Handle(ctx context.Context, req *domain.CreateBookingRequest, userInfo auth.User) (*domain.CreateBookingResponse, error)
}

type createBookingHandler struct {
	cfg              *config.Schema
	bookingRepo      repositories.BookingRepository
	pnrRepo          repositories.PNRRepository
	bookingService   service.BookingService
	sessionService   service.SessionService
	bookingRedisRepo redisRepo.BookingRepository
	orderClient      order.OrderServiceClient
	reportService    service.ReportService
	l2bSvc           service.L2bService
	listFlightSvc    service.ListFlightService
}

func NewCreateBookingHandler(
	cfg *config.Schema,
	bookingRepo repositories.BookingRepository,
	pnrRepo repositories.PNRRepository,
	bookingService service.BookingService,
	sessionService service.SessionService,
	bookingRedisRepo redisRepo.BookingRepository,
	orderClient order.OrderServiceClient,
	reportService service.ReportService,
	l2bSvc service.L2bService,
	listFlightSvc service.ListFlightService,
) CreateBookingHandler {
	return &createBookingHandler{
		cfg,
		bookingRepo,
		pnrRepo,
		bookingService,
		sessionService,
		bookingRedisRepo,
		orderClient,
		reportService,
		l2bSvc,
		listFlightSvc,
	}
}

func (h *createBookingHandler) Handle(ctx context.Context, req *domain.CreateBookingRequest, userInfo auth.User) (*domain.CreateBookingResponse, error) {
	h.l2bSvc.Look(ctx, req.OfficeID, enum.L2bAPICreateBooking)

	err := h.bookingRedisRepo.AcquireCreateBookingLock(req.SessionID)
	if err != nil {
		return nil, domain.ErrAnotherRequestInprocess
	}
	defer h.bookingRedisRepo.ReleaseCreateBookingLock(req.SessionID)

	ok, err := h.sessionService.Verify(ctx, req.OfficeID, req.SessionID)
	if err != nil {
		log.Error("sessionService.Verify error", log.Any("error", err), log.String("officeID", req.OfficeID), log.String("session", req.SessionID))
		return nil, commonError.ErrSomethingOccurred
	}

	if !ok {
		return &domain.CreateBookingResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess:  false,
				ErrorCode:  domain.ErrSessionInvalidOrExpired.Error(),
				StatusCode: http.StatusBadRequest,
			},
		}, nil
	}

	pnr, err := h.pnrRepo.FindOne(ctx, req.SessionID)
	if err != nil {
		log.Error("pnrRepo.FindOne error", log.Any("error", err), log.String("session", req.SessionID))
		return nil, commonError.ErrSomethingOccurred
	}

	booking, err := h.bookingRepo.FindBookingBySessionID(ctx, req.SessionID)
	if err != nil {
		log.Error("bookingRepo.FindBookingBySessionID error", log.Any("error", err), log.String("session", req.SessionID))
		return nil, commonError.ErrSomethingOccurred
	}

	if booking.BookingCode != "" && booking.BookingRef != "" {
		return nil, domain.ErrBookingExisted
	}

	if booking.FareDataCf == nil {
		return nil, domain.ErrTotalFareNotConfirmed
	}

	bookingDetails := domain.BookingDetails{
		OfficeID:        booking.OfficeID,
		FlightType:      booking.FlightType,
		Itineraries:     booking.Itineraries,
		FareDataCf:      booking.FareDataCf,
		ListPax:         pnr.ListPax,
		ContactInfo:     pnr.ContactInfo,
		Status:          enum.BookingStatusOK,
		AirlineSystem:   booking.AirlineSystem,
		VAT:             booking.VAT,
		PendingDeadline: booking.PendingDeadline,
		OriginFareData:  booking.OriginFareData,
		Tag:             enum.TagNone,
		Metadata:        booking.Metadata,
	}

	if booking.Provider == enum.FlightProviderPkfare || booking.Provider == enum.FlightProviderTongCheng {
		bookingDetails.Tag = enum.TagBZT
	} else if booking.Provider == enum.FlightProviderAmadeus || booking.Provider.IsVJProvider() || booking.Provider.IsHNHProvider() {
		bookingDetails.Tag = enum.TagLLD
	}

	bookingDetails.BookingCode, err = h.genBookingCode(ctx)
	if err != nil {
		return nil, err
	}

	flightOrder := domain.ToDomainFlightOrder(&bookingDetails)
	flightOrder.UserID = req.AgentID

	orderID, err := h.orderClient.CreateFlightOrder(ctx, flightOrder)
	if err != nil {
		log.Error("h.orderClient.CreateFlightOrder error", log.Any("error", err), log.String("sessionID", req.SessionID), log.Any("flightOrder", flightOrder))
		return nil, commonError.ErrSomethingOccurred
	}

	skipDefaultLastTicketDate := false

	res, err := h.bookingService.CreateBooking(ctx, booking.Provider, &bookingDetails, booking, booking.SearchRequest, pnr, booking.SessionID, false)
	if err != nil {
		if errors.Is(err, domain.ErrItinerarySoldOut) {
			go h.setSoldOutFlight(booking.FlightID)
		}

		if errors.Is(err, domain.ErrSeatNotAvailable) {
			_ = h.bookingRepo.UpdateBooking(ctx, req.SessionID, &domain.UpdateBookingRepoRequest{
				Status: enum.BookingStatusCancelled,
			})
		}

		return nil, err
	}

	bookingRef := res.BookingRef
	bookingDetails.LastTicketingDate = res.LastTicketingDate

	for _, iti := range bookingDetails.Itineraries {
		iti.ReservationCode = res.BookingRef
	}

	// Hard code version
	if req.Version == 2 {
		skipDefaultLastTicketDate = res.SkipDefaultLastTktDate && booking.Provider == enum.FlightProviderAmadeus
	}

	if !skipDefaultLastTicketDate && (bookingDetails.LastTicketingDate == 0) {
		bookingDetails.LastTicketingDate = time.Now().Add(time.Duration(h.cfg.IssueDurationDefault) * time.Minute).UnixMilli()
	}

	confirmed := true

	updateBookingRepoReq := &domain.UpdateBookingRepoRequest{
		BookingCode:       bookingDetails.BookingCode,
		BookingRef:        bookingRef,
		Status:            bookingDetails.Status,
		LastTicketingDate: bookingDetails.LastTicketingDate,
		FareExpiredDate:   &res.FareExpiredDate,
		TicketExpiredDate: &res.TicketExpiredDate,
		OrderID:           orderID,
		ExpectedPrice:     res.ExpectedPrice,
		CommissionRate:    res.CommRate,
		Confirmed:         &confirmed,
		ReservationCode:   &bookingRef,
		OrderNumRef:       res.OrderNumRef,
		InternalBooking:   &booking.InternalBooking,
		IsVJ24h:           &booking.IsVJ24h,
	}

	if res.AirlineSystem != "" {
		updateBookingRepoReq.AirlineSystem = res.AirlineSystem
	}

	err = h.bookingRepo.UpdateBooking(ctx, req.SessionID, updateBookingRepoReq)
	if err != nil {
		log.Error("bookingRepo.UpdateBooking error", log.Any("error", err), log.String("sessionID", req.SessionID))
		return nil, commonError.ErrSomethingOccurred
	}

	err = h.sessionService.Close(ctx, req.SessionID)
	if err != nil {
		return nil, err
	}

	booking, err = h.bookingRepo.FindBookingBySessionID(ctx, req.SessionID)
	if err != nil {
		log.Error("bookingRepo.FindBookingBySessionID error", log.Any("error", err), log.String("session", req.SessionID))
		return nil, commonError.ErrSomethingOccurred
	}

	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), constants.DefaultCtxTimeout)
		defer cancel()

		err = h.reportService.SendReportBooking(ctx, booking, pnr)
		if err != nil {
			log.Error("reportService.SendReportBooking error", log.Any("error", err))
		}

	}()

	bookingDetails.Transferable = booking.IsTransferable()

	response := &domain.CreateBookingResponse{
		BookingDetails: bookingDetails,
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
	}

	if booking.HasEMDSeat() {
		response.SeatBooking = &domain.ErrorRes{
			IsSuccess: true,
		}
	}

	if res.SeatError != nil {
		response.SeatBooking = &domain.ErrorRes{
			IsSuccess:  false,
			ErrorMsg:   res.SeatError.Error(),
			StatusCode: http.StatusBadRequest,
		}
	}

	return response, nil
}

func (h *createBookingHandler) genBookingCode(ctx context.Context) (code string, err error) {
	const (
		bookingCodeLength = 12
		retry             = 3
	)

	for i := 0; i < retry; i++ {
		code = helpers.GenerateBookingCode()

		check, err := h.bookingRepo.FindOneByBookingCodeV2(ctx, code)
		if err != nil {
			log.Error("bookingRepo.FindOneByBookingCode error", log.Any("error", err), log.String("code", code))
			return "", err
		}

		if check != nil || code == "" {
			continue
		}

		break
	}

	return
}

func (h *createBookingHandler) setSoldOutFlight(flightID string) {
	ctx, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
	defer cc()
	_ = h.listFlightSvc.SetFlightSoldOut(ctx, flightID)
}
