package domain

import (
	"fmt"
	"time"

	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

// ListBookingFilter represents booking filter criteria
type ListBookingFilter struct {
	// Bỏ required validation cho OfficeID
	OfficeID string `json:"office_id"`

	// Simple filters
	BookingCode  string               `json:"bookingCode,omitempty"`
	FromDate     *float64             `json:"fromDate,omitempty"` // Unix milliseconds
	ToDate       *float64             `json:"toDate,omitempty"`   // Unix milliseconds
	StatusesList []enum.BookingStatus `json:"statusesList,omitempty"`

	// Manager filters
	ManagerID       string   `json:"manager_id,omitempty"`
	ManageOfficeIDs []string `json:"manage_office_ids,omitempty"`
	PartnershipID   string   `json:"partnership_id,omitempty"`

	// Pagination
	Pagination *commonDomain.Pagination `json:"pagination,omitempty"`
}

// ListBookingResult represents the result with metadata
type ListBookingResult struct {
	Bookings   []*BookingSession        `json:"bookings"`
	Pagination *commonDomain.Pagination `json:"pagination"`
	Summary    *BookingListSummary      `json:"summary"`
	FilterInfo *FilterInfo              `json:"filter_info"`
}

// BookingListSummary contains summary statistics
type BookingListSummary struct {
	TotalBookings     int                         `json:"total_bookings"`
	TotalAmount       float64                     `json:"total_amount"`
	Currency          string                      `json:"currency"`
	StatusBreakdown   map[enum.BookingStatus]int  `json:"status_breakdown"`
	ProviderBreakdown map[enum.FlightProvider]int `json:"provider_breakdown"`
	TicketBreakdown   map[enum.TicketStatus]int   `json:"ticket_breakdown"`
}

// FilterInfo shows which filters were applied
type FilterInfo struct {
	HasBookingCodeFilter bool `json:"has_booking_code_filter"`
	HasDateFilter        bool `json:"has_date_filter"`
	HasStatusFilter      bool `json:"has_status_filter"`
	FilterCount          int  `json:"filter_count"`
}

// SetDefaults sets default values for the filter
func (f *ListBookingFilter) SetDefaults() {
	// Set default pagination
	if f.Pagination == nil {
		f.Pagination = &commonDomain.Pagination{
			PageCurrent: 1,
			PageLimit:   20,
		}
	}
}

// Validate validates the filter criteria
func (f *ListBookingFilter) Validate() error {
	// Bỏ validation office_id is required
	// if f.OfficeID == "" {
	//     return fmt.Errorf("office_id is required")
	// }

	// Validate date range
	if f.FromDate != nil && f.ToDate != nil {
		if *f.FromDate > *f.ToDate {
			return fmt.Errorf("fromDate cannot be after toDate")
		}
	}

	// Validate pagination
	if f.Pagination != nil {
		if f.Pagination.PageLimit > 100 {
			return fmt.Errorf("page_limit cannot exceed 100")
		}
		if f.Pagination.PageLimit <= 0 {
			f.Pagination.PageLimit = 20
		}
		if f.Pagination.PageCurrent <= 0 {
			f.Pagination.PageCurrent = 1
		}
	}

	return nil
}

// GetFilterInfo analyzes the filter and returns metadata
func (f *ListBookingFilter) GetFilterInfo() *FilterInfo {
	info := &FilterInfo{}

	// Check booking code filter
	if f.BookingCode != "" {
		info.HasBookingCodeFilter = true
		info.FilterCount++
	}

	// Check date filter
	if f.FromDate != nil || f.ToDate != nil {
		info.HasDateFilter = true
		info.FilterCount++
	}

	// Check status filter
	if len(f.StatusesList) > 0 {
		info.HasStatusFilter = true
		info.FilterCount++
	}

	return info
}

// ToTimeFilter converts float64 milliseconds to time.Time for repository
func (f *ListBookingFilter) ToTimeFilter() *ListBookingTimeFilter {
	timeFilter := &ListBookingTimeFilter{
		OfficeID:     f.OfficeID,
		BookingCode:  f.BookingCode,
		StatusesList: f.StatusesList,
		Pagination:   f.Pagination,
	}

	// Convert float64 milliseconds to time.Time
	if f.FromDate != nil {
		t := time.UnixMilli(int64(*f.FromDate))
		timeFilter.FromDate = &t
	}
	if f.ToDate != nil {
		t := time.UnixMilli(int64(*f.ToDate))
		timeFilter.ToDate = &t
	}

	return timeFilter
}

// ListBookingTimeFilter is the same as ListBookingFilter but with time.Time for dates
type ListBookingTimeFilter struct {
	// Required
	OfficeID string `json:"office_id"`

	// Simple filters
	BookingCode  string               `json:"bookingCode,omitempty"`
	FromDate     *time.Time           `json:"fromDate,omitempty"`
	ToDate       *time.Time           `json:"toDate,omitempty"`
	StatusesList []enum.BookingStatus `json:"statusesList,omitempty"`

	ManagerOffices []string `json:"manager_offices,omitempty"`
	// Pagination
	Pagination *commonDomain.Pagination `json:"pagination,omitempty"`
}

// GetFilterInfo analyzes the time filter and returns metadata
func (f *ListBookingTimeFilter) GetFilterInfo() *FilterInfo {
	info := &FilterInfo{}

	// Check booking code filter
	if f.BookingCode != "" {
		info.HasBookingCodeFilter = true
		info.FilterCount++
	}

	// Check date filter
	if f.FromDate != nil || f.ToDate != nil {
		info.HasDateFilter = true
		info.FilterCount++
	}

	// Check status filter
	if len(f.StatusesList) > 0 {
		info.HasStatusFilter = true
		info.FilterCount++
	}

	return info
}
