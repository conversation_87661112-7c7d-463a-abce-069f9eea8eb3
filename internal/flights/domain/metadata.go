package domain

const (
	MetaKey                    = ""
	MetaKeyBaseTotalFareAmount = "base_total_fare_amount"
	MetaKeyTotalTaxAmount      = "total_tax_amount"
	MetaKeyCurrency            = "currency"
	MetaKeyOriginFare          = "origin_fare"
	MetaKeyTotalPaxFare        = "total_pax_fare"
	MetaKeySolution            = "solution"
)

type Metadata struct {
	Key   string      `json:"-"`
	Value interface{} `json:"-"`
}
