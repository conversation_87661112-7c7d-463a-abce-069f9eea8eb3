package flights

import (
	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/internal_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/price"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/telegram"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	amadeus_client_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/amadeus_client"
	data_warehouse_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/data_warehouse"
	ev_client_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/ev_client"
	ev_international_client_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/ev_international_client"
	hnh_client_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/hnh_client"
	repositories_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/mongodb/repositories"
	order_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/order"
	payment_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/payment"
	pkfare_client_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/pkfare_client"
	tongcheng_client_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/tongcheng_client"
	travel_fusion_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/travel_fusion"
	vietjet_client_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/vietjet_client"
	vna1a_client_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/vna1a_client"
	vna_client_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/vna_client"
	wallet_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/wallet"
	webhook_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/adapter/webhook"
	command_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/app/command"
	query_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/app/query"
	service_tracing "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/tracing/app/service"

	redisRepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/port/background"
	port "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/port/grpc"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/port/http"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

func New(cfg *config.Schema, db commonMongoDB.DB, redisClient redis.IRedis, internalClient internal_client.InternalClient) (*http.Server, background.Cronjob, *port.Server) {
	requestRepo := repositories_tracing.NewRequestRepository(db, cfg)

	// Mongo repo
	searchFlightRepo := repositories_tracing.NewSearchFlightsRepository(db)
	listFlightRepo := repositories_tracing.NewListFlightRepository(db)
	sessionRepo := repositories_tracing.NewSessionRepository(db)
	bookingRepo := repositories_tracing.NewBookingRepository(db)
	airportRepo := repositories_tracing.NewAirportRepository(db)
	aircraftLayoutRepo := repositories_tracing.NewAircraftLayoutRepository(db)
	seatMapRepo := repositories_tracing.NewSeatMapRepository(db)
	pnrRepo := repositories_tracing.NewPNRRepository(db)
	amadeusPNRRepo := repositories_tracing.NewAmadeusPNRRepository(db)
	currencyExRepo := repositories_tracing.NewCurrencyExchangeRepository(db)
	supplierRepo := repositories_tracing.NewSupplierRouteRepository(db)
	tfRequestRoutingRepo := repositories_tracing.NewTFRoutingRepository(db)
	commRepo := repositories_tracing.NewCommissionRepository(db)
	l2bRepo := repositories_tracing.NewL2bRepository(db)
	baggageOptionRepo := repositories_tracing.NewBaggageOptionRepository(db)
	amadeusISOCountryRepo := repositories_tracing.NewAmadeusISOCountryRepository(db)
	amaSSRTemplateRepo := repositories_tracing.NewAmaSSRTemplateRepository(db)
	salesforceConfigRepo := repositories_tracing.NewSalesforceConfigRepository(db)

	// Redis repo
	pnrRedis := redisRepo.NewPNRRedisRepository(redisClient)
	bookingRedis := redisRepo.NewBookingRedisRepository(redisClient)
	ticketRedis := redisRepo.NewTicketRedisRepository(redisClient)
	currencyExRedis := redisRepo.NewCurrencyExchangeRepository(redisClient)
	supplierRedis := redisRepo.NewSupplierRepository(cfg, redisClient)
	searchFlightsRedis := redisRepo.NewSearchFlightsRepository(redisClient)
	tfRequestRoutingRedis := redisRepo.NewTFRoutingRepositories(redisClient)
	l2bRedis := redisRepo.NewL2bRepository(redisClient)
	tokenRedis := redisRepo.NewTokenRedisRepository(redisClient)
	ancillaryRedis := redisRepo.NewAncillaryRepository(redisClient)

	// Adapter, client & other stuff
	amadesus := amadeus_client_tracing.NewAmadeusAdapter(cfg, requestRepo, amadeusISOCountryRepo, redisClient, airportRepo, amaSSRTemplateRepo)
	data_warehouse := data_warehouse_tracing.NewDataWareHouseServiceClient(cfg)
	evInternationalClient := ev_international_client_tracing.NewEVInternationalClient(cfg, requestRepo, tokenRedis)
	evInternationalAdapter := ev_international_client_tracing.NewEVInternationalAdapter(evInternationalClient, cfg)
	vnaClient := vna_client_tracing.NewVNAAdapter(cfg, requestRepo)
	telegramRepo := telegram.NewEVIssueTicketTelegramRepository(cfg)
	issueEMDTelegramRepo := telegram.NewIssueEMDRepository(cfg)
	vjAdapter := vietjet_client_tracing.NewVietjetAdapter(cfg, redisClient, requestRepo)
	evAdapter := ev_client_tracing.NewEVAdapter(cfg, redisClient, requestRepo)
	travelFusionClient := travel_fusion_tracing.NewTravelFusionClient(cfg, requestRepo, redisClient)
	travelFusionAdapter := travel_fusion_tracing.NewTravelFusionAdapter(cfg, travelFusionClient, tfRequestRoutingRepo, tfRequestRoutingRedis)
	pkfareAdapter := pkfare_client_tracing.NewPkfareAdapter(cfg, requestRepo)
	tongChengAdapter := tongcheng_client_tracing.NewTongChengAdapter(cfg, requestRepo)
	// vnaClient.IssueEMD(context.Background(), "TOSXVE", "TOSXVE")
	// Grpc clients
	orderClient := order_tracing.NewOrderServiceClient(cfg)
	walletClient := wallet_tracing.NewWalletClient(cfg)
	paymentClient := payment_tracing.NewPaymentClient(cfg)
	webhookClient := webhook_tracing.NewWebhookAdapter(cfg, requestRepo)
	vna1aClient := vna1a_client_tracing.NewVNA1AAdapter(cfg, requestRepo)
	hnhClient := hnh_client_tracing.NewHNHAdapter(cfg, requestRepo)
	priceClient := price.NewPriceClient(cfg)
	// Services
	priceService := service_tracing.NewPriceService(priceClient)
	l2bSvc := service_tracing.NewL2bService(l2bRedis)
	commSvc := service_tracing.NewCommissionService(commRepo)
	reportService := service_tracing.NewReportService(cfg, data_warehouse, internalClient, salesforceConfigRepo)
	listFlightSvc := service_tracing.NewListFlightService(listFlightRepo, searchFlightRepo)
	supplierService := service_tracing.NewSupplierRouteService(travelFusionAdapter, supplierRepo, supplierRedis)
	currencyExSvc := service_tracing.NewCurrencyExchangeService(currencyExRepo, currencyExRedis)
	fareService := service_tracing.NewFareService(cfg, vnaClient, vjAdapter, travelFusionAdapter, amadesus, evAdapter, evInternationalAdapter, amadeusPNRRepo, bookingRepo, vna1aClient, webhookClient, hnhClient, pkfareAdapter, priceService, tongChengAdapter)
	ancillaryService := service_tracing.NewAncillaryService(amadesus, vjAdapter, vnaClient, travelFusionAdapter, aircraftLayoutRepo, seatMapRepo, baggageOptionRepo, ancillaryRedis, supplierService, listFlightSvc, currencyExSvc, hnhClient)
	sessionService := service_tracing.NewSessionService(sessionRepo)
	bookingService := service_tracing.NewBookingService(bookingRepo, vnaClient, amadeusPNRRepo, travelFusionAdapter, pnrRepo, amadesus, vjAdapter, evInternationalAdapter, evAdapter, ancillaryService, vna1aClient, reportService, hnhClient, pkfareAdapter, cfg)
	pnrService := service_tracing.NewPNRService(pnrRepo, bookingRepo, seatMapRepo, baggageOptionRepo)
	issueService := service_tracing.NewIssueTicketService(cfg, vnaClient, travelFusionAdapter, amadesus, vjAdapter, bookingRepo, pnrRepo, bookingRedis, telegramRepo, walletClient, paymentClient, internalClient, webhookClient, commSvc, l2bSvc, evAdapter, ancillaryService, amadeusPNRRepo, issueEMDTelegramRepo, vna1aClient, hnhClient, reportService, pkfareAdapter)
	// TODO: Uncomment when command handlers are implemented
	searchHandler := service.NewProviderSearchHandler(amadesus, travelFusionAdapter, vnaClient, vjAdapter, evAdapter, evInternationalAdapter, supplierService, currencyExSvc, vna1aClient, hnhClient, pkfareAdapter)
	searchFlightsService := service_tracing.NewSearchFlightsService(cfg, searchFlightRepo, listFlightRepo, airportRepo, searchHandler, searchFlightsRedis, commSvc, priceService)
	amadeusService := service_tracing.NewAmadeusService(amadesus)

	// Commands & queries
	application := app.Application{
		Commands: app.Commands{
			CheckFareHandler:                        command_tracing.NewCheckFareHandler(fareService, sessionService, searchFlightsService, bookingService, supplierService, l2bSvc, searchFlightsRedis, listFlightRepo, listFlightSvc, currencyExSvc, priceService, *cfg),
			AddPNRHandler:                           command_tracing.NewAddPNRHandler(pnrService, sessionService, pnrRedis, l2bSvc, pnrRepo, bookingRepo, seatMapRepo, baggageOptionRepo),
			ModPNRHandler:                           command_tracing.NewModPNRHandler(sessionService, pnrService, pnrRedis, bookingService, l2bSvc),
			ConfirmFareHandler:                      command_tracing.NewConfirmFareHandler(sessionService, fareService, bookingRepo, pnrRepo, bookingService, currencyExSvc, supplierService, l2bSvc, listFlightSvc, priceService),
			CreateBookingHandler:                    command_tracing.NewCreateBookingHandler(cfg, bookingRepo, pnrRepo, bookingService, sessionService, bookingRedis, orderClient, reportService, l2bSvc, listFlightSvc),
			IssueTicketHandler:                      command_tracing.NewIssueTicketHandler(bookingRepo, pnrRepo, issueService, bookingService, ticketRedis, orderClient, reportService, webhookClient, paymentClient, walletClient, internalClient, l2bSvc, listFlightSvc, query_tracing.NewGetBookingByIDHandler(bookingRepo, pnrRepo)),
			ProcessPendingTicket:                    command_tracing.NewProcessPendingTicketHandler(bookingRepo, issueService, internalClient, bookingService),
			ProcessSaveMultipleSupplierRoutesDaily:  command_tracing.NewProcessSaveMultipleSupplierRoutesDailyHandler(supplierService),
			ProcessExpiredBookingHandler:            command_tracing.NewProcessExpiredBookingHandler(sessionService, bookingService),
			DeleteExpiredRoutingData:                command_tracing.NewDeleteExpiredRoutingDataHandler(tfRequestRoutingRepo),
			BulkInsertL2bItem:                       command_tracing.NewBulkInsertL2bItemHandler(l2bRepo, l2bRedis),
			UpdateTicketAndReservationCode:          command_tracing.NewUpdateTicketAndReservationCodeHandler(bookingRepo),
			ManualCancelBooking:                     command_tracing.NewManualCancelBookingHandler(bookingRepo, issueService),
			UpdateBookingTransfer:                   command_tracing.NewUpdateBookingTransferHandler(bookingRepo),
			RetrievePNRAmadeusForTransferredBooking: command_tracing.NewRetrievePNRAmadeusForTransferredBookingHandler(bookingRepo, amadeusService, pnrRepo),
			CheckFareV2Handler:                      command_tracing.NewCheckFareV2Handler(fareService, sessionService, searchFlightsService, bookingService, supplierService, l2bSvc, searchFlightsRedis, listFlightSvc, priceService, *cfg),
			CreateAmaSSRTemplate:                    command_tracing.NewCreateAmaSSRTemplateHandler(amaSSRTemplateRepo),
			UpdateAmaSSRTemplate:                    command_tracing.NewUpdateAmaSSRTemplateHandler(amaSSRTemplateRepo),
			SetBookingNotified:                      command_tracing.NewSetBookingNotifiedHandler(bookingRepo),
			PrepareBookingHandler:                   command.NewPrepareBookingHandler(bookingService, bookingRepo, pnrRepo, listFlightSvc),
			CreateCurrencyExchangeHandler:           command_tracing.NewCreateCurrencyExchangeHandler(currencyExRepo),
			UpdateCurrencyExchangeHandler:           command_tracing.NewUpdateCurrencyExchangeHandler(currencyExRepo),
		},
		Queries: app.Queries{
			SearchFlightsHandler:         query_tracing.NewSearchFlightsHandler(cfg, travelFusionClient, searchFlightsService, currencyExSvc, supplierService, l2bSvc),
			RetrieveBookingHandler:       query_tracing.NewRetrieveBookingHandler(cfg, bookingRepo, bookingService, pnrRepo, issueService, l2bSvc),
			LoginHandler:                 query_tracing.NewLoginHandler(cfg, internalClient),
			TransactionHistoryHandler:    query_tracing.NewTransactionHistoryHandler(walletClient),
			ListBooking:                  query_tracing.NewListBookingHandler(bookingRepo),
			ListBookingByTicketStatuses:  query_tracing.NewListBookingByTicketStatusesHandler(bookingRepo),
			ListExpiredBooking:           query_tracing.NewListExpiredBookingHandler(bookingRepo),
			GetSeatMap:                   query_tracing.NewGetSeatMapHandler(searchFlightsService, ancillaryService, bookingRepo, listFlightSvc),
			GetInMemL2b:                  query_tracing.NewGetInMemL2bHandler(l2bRedis),
			GetL2b:                       query_tracing.NewGetL2bHandler(l2bRepo),
			GetBalanceHandler:            query_tracing.NewGetBalanceHandler(walletClient),
			GetBookingByCode:             query_tracing.NewGetBookingByCodeHandler(bookingRepo),
			GetPRNBySession:              query_tracing.NewGetPNRBySessionHandler(pnrRepo),
			GetBaggages:                  query_tracing.NewGetBaggagesHandler(baggageOptionRepo, searchFlightsService, ancillaryService, listFlightSvc),
			GetReportBooking:             query_tracing.NewGetReportBookingHandler(bookingRepo, pnrRepo, internalClient, reportService),
			ListUpcomingBookings:         query_tracing.NewListUpcomingBookingsHandler(bookingRepo),
			GetOfficeInfo:                query_tracing.NewGetOfficeInfoHandler(internalClient),
			SearchFlightsV2Handler:       query_tracing.NewSearchFlightsV2Handler(cfg, travelFusionClient, searchFlightsService, currencyExSvc, supplierService, l2bSvc),
			ListCurrencyExchanggeHandler: query_tracing.NewListCurrencyExchangeHandler(currencyExRepo),
			GetCurrencyExchangeHandler:   query_tracing.NewGetCurrencyExchangeDetailHandler(currencyExRepo),
			ListBookingFilterHandler:     query_tracing.NewListBookingFilterHandler(bookingRepo, internalClient),
			GetBookingByID:               query_tracing.NewGetBookingByIDHandler(bookingRepo, pnrRepo),
		},
		Services: app.Services{
			BookingEnrichment: service.NewBookingEnrichmentService(pnrRepo, internalClient),
		},
	}

	bgPort := background.NewBackgroundJob(cfg, application)

	return http.New(application), bgPort, port.NewServer(application)
}
